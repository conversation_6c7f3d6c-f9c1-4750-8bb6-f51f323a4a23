import os
import time


def report_execution_time(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"Execution time: {end_time - start_time} seconds")
        return result

    return wrapper


def inc(x):
    return x + 1


@report_execution_time
def test_answer():
    assert inc(3) == 4


@report_execution_time
def test_bad_answer():
    assert not inc(3) == 5


@report_execution_time
def main_test():
    os.system("python -m pytest ..")


if __name__ == "__main__":
    main_test()
