import os
import json
import pytest
import boto3
from botocore.exceptions import Client<PERSON>rror
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get S3 configuration from environment variables
BUCKET_NAME = os.getenv("S3_BUCKET_NAME")
AWS_ACCESS_KEY_ID = os.getenv("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = os.getenv("AWS_SECRET_ACCESS_KEY")
AWS_REGION = os.getenv("AWS_REGION")

# Test data
TEST_JSON_DATA = {"test_key": "test_value", "nested": {"key": "value"}}
TEST_FILE_NAME = "test_file.json"


@pytest.fixture
def s3_client():
    """Create an S3 client for testing"""
    return boto3.client(
        "s3",
        aws_access_key_id=AWS_ACCESS_KEY_ID,
        aws_secret_access_key=AWS_SECRET_ACCESS_KEY,
        region_name=AWS_REGION,
    )


def test_write_json_to_s3(s3_client):
    """Test writing a JSON file to S3"""
    # Convert JSON data to string
    json_data = json.dumps(TEST_JSON_DATA)

    # Upload to S3
    s3_client.put_object(
        Bucket=BUCKET_NAME,
        Key=TEST_FILE_NAME,
        Body=json_data,
        ContentType="application/json",
    )

    # Verify the file exists
    try:
        s3_client.head_object(Bucket=BUCKET_NAME, Key=TEST_FILE_NAME)
        assert True
    except ClientError as e:
        if e.response["Error"]["Code"] == "404":
            assert False, "File was not uploaded successfully"
        else:
            raise


def test_read_json_from_s3(s3_client):
    """Test reading a JSON file from S3"""
    # Get the object from S3
    response = s3_client.get_object(Bucket=BUCKET_NAME, Key=TEST_FILE_NAME)

    # Read and parse the JSON data
    json_data = json.loads(response["Body"].read().decode("utf-8"))

    # Verify the data matches what we wrote
    assert json_data == TEST_JSON_DATA


def test_cleanup(s3_client):
    """Clean up test files after testing"""
    try:
        s3_client.delete_object(Bucket=BUCKET_NAME, Key=TEST_FILE_NAME)
    except ClientError as e:
        print(f"Error cleaning up test file: {e}")
