import dagger
from dagger import dag, function, object_type


@object_type
class SlackGenie:
    @function
    def container_echo(self, string_arg: str) -> str:
        """Returns a container that echoes whatever string argument is provided"""
        return (
            dag.container()
            .from_("alpine:latest")
            .with_exec(["echo", string_arg])
            .stdout()
        )

    @function
    async def grep_dir(self, directory_arg: dagger.Directory, pattern: str) -> str:
        """Returns lines that match a pattern in the files of the provided Directory"""
        return await (
            dag.container()
            .from_("alpine:latest")
            .with_mounted_directory("/mnt", directory_arg)
            .with_workdir("/mnt")
            .with_exec(["grep", "-R", pattern, "."])
            .stdout()
        )

    @function
    def check_with_config(
        self, source: dagger.Directory, file: dagger.File
    ) -> dagger.Container:
        return dag.ruff().check_with_config(source, file)

    @function
    def check(self, source: dagger.Directory) -> dagger.Container:
        return dag.ruff().check(source)

    @function
    def build(self, source: dagger.Directory) -> dagger.Container:
        """Build the application container"""
        return self.build_env(source).with_exec(["$POETRY_HOME/bin/poetry", "build"])

    @function
    async def test(self, source: dagger.Directory) -> str:
        """Return the result of running unit tests"""
        return await (
            self.build_env(source)
            .with_exec(["$POETRY_HOME/bin/poetry", "run", "pytest"])
            .stdout()
        )

    @function
    def build_env(self, source: dagger.Directory) -> dagger.Container:
        """Build a ready-to-use development environment"""
        python_cache = dag.cache_volume("python")
        poetry_cache = dag.cache_volume("poetry")
        project_cache = dag.cache_volume("project")
        return (
            dag.container()
            .from_("python:3.12-slim")
            .with_directory("/src", source)
            .with_mounted_cache("/root/.local", python_cache)
            .with_mounted_cache("/opt/poetry", poetry_cache)
            .with_mounted_cache("/src/.venv", project_cache)
            .with_workdir("/src")
            .with_exec(["pip", "install", "--user", "pipx"])
            .with_env_variable("POETRY_HOME", "/opt/poetry")
            .with_exec(["python3", "-m", "venv", "$POETRY_HOME"])
            .with_exec(["$POETRY_HOME/bin/pip", "install", "poetry==2.1.1"])
            .with_exec(["$POETRY_HOME/bin/poetry", "--version"])
            .with_exec(
                ["$POETRY_HOME/bin/poetry", "self", "add", "poetry-plugin-export"]
            )
            .with_exec(
                ["$POETRY_HOME/bin/poetry", "config", "virtualenvs.create", "true"]
            )
            .with_exec(
                ["$POETRY_HOME/bin/poetry", "config", "virtualenvs.in-project", "true"]
            )
            .with_exec(["$POETRY_HOME/bin/poetry", "env", "use", "python3"])
            .with_exec(["$POETRY_HOME/bin/poetry", "install", "--with", "dev,test"])
        )
