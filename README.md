# SlackGenie - AI-Powered Slack Bot for Databricks

This project is a Slack bot that integrates with Databricks Genie, providing an intelligent interface for users to interact with their Databricks workspace through natural language queries.

## Setup

1. Clone the repository.

2. Install the `just` command runner (if not already installed):
   ```sh
   # macOS
   brew install just
   
   # Linux
   # Visit https://just.systems/man/en/prerequisites.html for installation instructions
   ```

3. Setup the development environment:
   ```sh
   # This will:
   # - Create a .env file from .env.sample
   # - Install mise and brew (if needed)
   # - Install dependencies via Brewfile
   # - Configure Poetry and create a virtual environment
   # - Install Python dependencies
   just app-setup
   ```

4. Run the application:
   ```sh
   # This will:
   # - Start PostgreSQL in Docker
   # - Launch the application
   just app-run
   ```

5. Open the application in your browser (in a new terminal):
   ```sh
   # This will open various application endpoints in Chrome
   just open
   ```

## Database Management

1. Create and update PostgreSQL database:
   ```sh
   # Start PostgreSQL
   docker compose up -d postgres
   
   # Apply migrations
   poetry run alembic upgrade head
   ```

2. Generate new migrations after schema changes:
   ```sh
   # Create migration
   poetry run alembic revision --autogenerate -m "Name of migration"
   
   # Apply the migration
   poetry run alembic upgrade head
   ```

## Additional Commands

- Format code: `just app-format`
- Run linters: `just app-lint`
- Run tests: `just app-test`
- Docker operations:
  - Build Docker image: `just docker-build`
  - Run in Docker: `just docker-run`
  - Run in Docker detached: `just docker-run-detached`
  - Stop Docker containers: `just docker-stop`
  - Clean Docker: `just docker-clean`

## Architecture

The application is built with a modular architecture that integrates Slack with Databricks Genie:

### Core Components

#### Slack Integration
- `slack_app.py`: Main Slack bot application handling events and messages
- `slack/handler.py`: FastAPI adapter for Slack events
- `routes.py`: API routes for handling Slack events

#### Chatbot Core
- `chatbot/core/graph.py`: Orchestrates the conversation flow using LangGraph
- `chatbot/core/states.py`: Manages conversation state
- `chatbot/core/nodes.py`: Defines processing nodes for the conversation graph
- `chatbot/core/edge.py`: Handles routing between different processing nodes

#### Tools
- `chatbot/tools/databricks_tool.py`: Integrates with Databricks Genie API
- `chatbot/tools/vector_store.py`: Vector database integration (for future use)
- `chatbot/tools/api_tool.py`: General API integration capabilities
- `chatbot/tools/collected_tools.py`: Central registry of available tools

#### Configuration
- `config.py`: Manages application settings and environment variables
- `resources/prompt_templates.py`: Contains prompt templates for the AI agent

### Flow
1. User sends a message in Slack
2. The message is processed by the Slack bot application
3. The conversation graph orchestrates the flow:
   - Agent node processes the message
   - Supervisor router determines if tools are needed
   - Tool nodes execute necessary actions (e.g., Databricks queries)
   - Results are formatted and sent back to the user

## AWS Infrastructure

The application is deployed on AWS using a serverless architecture that ensures scalability, security, and reliability.

![SlackGenie AWS Architecture](docgen/output/slackgenie_architecture.png)

### Components

#### API Layer
- **WAF (Web Application Firewall)**: Protects the API from common web exploits
- **API Gateway**: Manages API endpoints and routes requests to the Lambda function

#### Compute Layer
- **Lambda**: Hosts the FastAPI application in a serverless environment
- **ECR (Elastic Container Registry)**: Stores Docker container images for Lambda deployment

#### Storage & State
- **S3**: Stores Terraform state files
- **DynamoDB**: Provides state locking for Terraform operations

#### Monitoring & Security
- **CloudWatch**: Monitors application performance and logs
- **IAM Roles**: Manages security and access permissions

#### CI/CD Pipeline
- **CodeBuild**: Automates building and deploying container images
- Integrates with ECR for container image management

### Infrastructure Management
The entire infrastructure is managed as code using Terraform, enabling consistent and repeatable deployments across environments. For detailed infrastructure setup instructions, see the [terraform/README.md](terraform/README.md).
