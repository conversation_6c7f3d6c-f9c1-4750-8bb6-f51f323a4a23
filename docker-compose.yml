services:
  chatbot:
    env_file: ".env"
    ports:
      - "8080:8080"
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      APPS_USER: "admin" #${CHATBOT_BASIC_AUTH_USER}
      APPS_PASSWORD: "111" #${CHATBOT_BASIC_AUTH_PASSWORD}
    depends_on:
      - postgres
  postgres:
    env_file: ".env"
    image: "postgres:13"
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: hi #${POSTGRES_USER}
      POSTGRES_PASSWORD: itsme #${POSTGRES_PASSWORD}
      POSTGRES_DB: demo_db #${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      
volumes:
  postgres_data: