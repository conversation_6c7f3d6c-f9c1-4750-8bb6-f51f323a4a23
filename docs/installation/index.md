# Installation Guide

This guide will walk you through the installation process for SlackGenie.

## Prerequisites

- Python 3.12 or higher
- Docker (for running PostgreSQL)
- Slack workspace with admin access
- Just (command runner) - will be installed automatically if not present

## Installation Steps

1. Clone the repository:
   ```shell
   git clone https://github.com/yourusername/SlackGenie.git
   cd SlackGenie
   ```

2. Set up the development environment:
   ```shell
   just app-setup
   ```
   This command will:
   - Create a `.env` file from `.env.sample` if it doesn't exist
   - Install Mise (if not present)
   - Install Homebrew dependencies
   - Set up Python virtual environment
   - Install all project dependencies

3. Initialize the database:
   ```shell
   just docker-run-detached-for-service postgres
   poetry run alembic upgrade head
   ```

## Configuration

After installation, you'll need to configure your Slack workspace and Databricks integration. See the [Setup Guide](../setup/index.md) for detailed instructions.

## Additional Commands

Here are some useful commands for development:

- `just app-run` - Run the application locally
- `just app-test` - Run tests
- `just app-format` - Format code
- `just app-lint` - Run linters
- `just app-fix` - Fix formatting and linting issues
- `just app-check` - Check if the application is running properly 