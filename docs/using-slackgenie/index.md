# Using SlackGenie API

This guide will help you understand and use the SlackGenie API endpoints effectively.

## Base URL

The API is deployed at `https://mydomain.org/mysite` (replace with your actual domain).

## API Documentation

The API includes automatically generated interactive documentation using FastAPI's built-in Swagger UI and ReDoc:

- **Swagger UI Documentation:** `https://your-domain.com/docs`
  - Interactive documentation where you can try out the API endpoints directly
  - Includes detailed request/response schemas
  - Allows you to test endpoints with custom parameters

- **ReDoc Documentation:** `https://your-domain.com/redoc`
  - Alternative documentation interface with a more readable format
  - Better suited for sharing with non-technical users
  - Provides a clean, organized view of all endpoints

Both documentation interfaces are automatically generated from the API's code and provide up-to-date information about all available endpoints, their parameters, and response formats.

## Available Endpoints

### 1. Slack Events Endpoint

**Endpoint:** `POST /slack/events`

This endpoint handles all Slack events, including the initial verification and message events.

#### Request Format

```json
{
    "token": "uGUNJuU4R5Q7BeA7MaDuMVMi",
    "team_id": "T03G61VPV",
    "context_team_id": "T03G61VPV",
    "context_enterprise_id": null,
    "api_app_id": "A08G5BNFKBQ",
    "event": {
        "user": "U08DKQG1XM3",
        "type": "message",
        "ts": "**********.147579",
        "client_msg_id": "4df1cbb2-1e5d-4107-a416-0550c1c1f648",
        "text": "Your message here",
        "team": "T03G61VPV",
        "blocks": [...],
        "channel": "D08H2H95X5E",
        "event_ts": "**********.147579",
        "channel_type": "im"
    },
    "type": "event_callback",
    "event_id": "Ev08GN8TFC5D",
    "event_time": **********,
    "authorizations": [...],
    "is_ext_shared_channel": false,
    "event_context": "..."
}
```

### 2. Health Check Endpoint

**Endpoint:** `GET /health`

Performs a health check on the API service.

#### Response

```json
{
    "status": "OK"
}
```

### 3. Version Check Endpoint

**Endpoint:** `GET /version`

Returns the current version of the API.

#### Response

```json
{
    "status": "OK",
    "version": "v1.0.0"  // Example version
}
```

### 4. Configuration Check Endpoint

**Endpoint:** `GET /config`

Returns the current configuration status and environment settings.

#### Response

```json
{
    "status": "OK",
    "errors": "",
    "env": {
        // Environment variables
    },
    "conf": {
        // Configuration settings
    }
}
```

## Authentication

For Slack events, the API uses Slack's built-in verification system. The token in the request is used to verify the authenticity of the request.

## Error Handling

The API includes global error handling that will return a 500 status code with the following response for any unhandled exceptions:

```json
{
    "message": "Internal server error"
}
```

## Example Usage

1. **Setting up Slack Events:**
   - Configure your Slack app's Event Subscriptions to point to your API endpoint
   - The URL should be: `https://your-domain.com/slack/events`
   - Slack will verify the endpoint by sending a challenge request

2. **Monitoring API Health:**
   ```bash
   curl https://your-domain.com/health
   ```

3. **Checking API Version:**
   ```bash
   curl https://your-domain.com/version
   ```

4. **Verifying Configuration:**
   ```bash
   curl https://your-domain.com/config
   ```

## Best Practices

1. Always monitor the health endpoint to ensure the API is running properly
2. Use the version endpoint to verify you're using the correct API version
3. Check the configuration endpoint if you encounter any issues
4. Implement proper error handling in your client applications

## Rate Limiting

The API may implement rate limiting to prevent abuse. Monitor the response headers for rate limit information.

## Support

For additional support or questions, please refer to the main documentation or contact the development team. 