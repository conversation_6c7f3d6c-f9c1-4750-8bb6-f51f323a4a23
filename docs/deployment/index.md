# Deployment Guide

This guide explains how to deploy SlackGenie to AWS using the provided Just commands.

## Prerequisites

Before starting the deployment process, ensure you have:
- AWS CLI installed and configured
- Terraform CLI installed
- Docker installed
- Access to the AWS account (************)
- Required AWS permissions

## Initial Setup

### 1. AWS CLI and Terraform Setup

First, ensure you have the necessary tools installed:

```bash
just aws-setup
```

This will install:
- AWS CLI (if not already installed)
- Terraform CLI (if not already installed)

### 2. AWS Authentication

Configure your AWS SSO access:

```bash
just aws-configure
```

When prompted:
- Enter the StartURL: `https://d-9067809b5b.awsapps.com/start/#`
- Set the region to: `us-east-1`
- Configure the profile `************_AdministratorAccess` in `~/.aws/credentials`

## Infrastructure Deployment

### 1. Initialize Infrastructure

Initialize the Terraform configuration:

```bash
just aws-init-infra
```

### 2. Deploy Development Infrastructure

Plan and apply the development infrastructure:

```bash
# Plan the infrastructure changes
just aws-init_plan-dev-infra

# Apply the infrastructure changes
just aws-apply-dev-infra
```

Or run both commands in sequence:

```bash
just aws-infra
```

## Application Deployment

### 1. Build and Deploy Docker Image

The following commands will build, upload, and deploy your application:

```bash
# Build and deploy everything in one command
just aws-deploy2
```

This command executes the following steps in sequence:
1. Logs into Amazon ECR
2. Builds the Docker image for Lambda
3. Uploads the image to ECR
4. Updates the Lambda function with the new image

### 2. Verify Deployment

You can verify the deployment using several commands:

```bash
# Test the API endpoints
just aws-lambda-invoke-dev2

# Open all endpoints in Chrome
just aws-lambda-invoke-dev2-open
```

### 3. Monitoring

Monitor your deployment using:

```bash
# Open CloudWatch and Lambda monitoring dashboards
just aws-lambda-monitor-dev

# View Lambda logs in CLI
just aws-lambda-monitor-dev-logs-cli

# Check last modified date of Lambda
just aws-lambda-last-modified
```

## Available Endpoints

After deployment, the following endpoints will be available:
- Main API: `https://62hzytt9f8.execute-api.us-east-1.amazonaws.com/dev/`
- API Documentation: `https://62hzytt9f8.execute-api.us-east-1.amazonaws.com/dev/docs`
- ReDoc Documentation: `https://62hzytt9f8.execute-api.us-east-1.amazonaws.com/dev/redoc`
- Configuration: `https://62hzytt9f8.execute-api.us-east-1.amazonaws.com/dev/config`
- Version: `https://62hzytt9f8.execute-api.us-east-1.amazonaws.com/dev/version`
- Health Check: `https://62hzytt9f8.execute-api.us-east-1.amazonaws.com/dev/health`

## Troubleshooting

If you encounter issues during deployment:

1. Check the Lambda logs using `just aws-lambda-monitor-dev-logs-cli`
2. Verify your AWS credentials are properly configured
3. Ensure all required environment variables are set
4. Check the CloudWatch logs for any error messages

For more detailed information about the infrastructure setup, refer to the Terraform configuration in the `terraform/environments/dev` directory.