# Schedule Management Commands

This document describes the schedule management commands available in SlackGenie for managing your automated scheduled messages.

## Available Commands

### `/schedule_msg` - Create Scheduled Messages

Create automated messages that run at specific times.

**Usage:**
```
/schedule_msg <message> | <schedule>
```

**Examples:**
- One-time: `/schedule_msg Send daily report | 2024-05-20 15:30`
- Recurring: `/schedule_msg Daily standup reminder | every day at 9:30am`
- Weekly: `/schedule_msg Weekly review | every friday at 4pm`

### `/list_schedules` - View Active Schedules

List all your active scheduled messages with details including:
- Message content (truncated if long)
- <PERSON>ron expression
- Next run time
- Schedule ID
- Delete buttons for easy management

**Usage:**
```
/list_schedules
```

**Features:**
- Shows only your own schedules
- Displays next run time in UTC
- Provides delete buttons with confirmation dialogs
- Shows schedule IDs for use with `/delete_schedule`

### `/delete_schedule` - Delete Specific Schedule

Delete a specific scheduled message by its ID.

**Usage:**
```
/delete_schedule <schedule_id>
```

**Example:**
```
/delete_schedule 8caf5def-c8e3-459c-84ac-826e87476f54
```

**Features:**
- Requires the exact schedule ID (UUID format)
- Only allows deleting your own schedules
- Automatically cleans up AWS EventBridge rules
- Provides confirmation of deletion

## Interactive Features

### Delete Buttons in List View

When you use `/list_schedules`, each schedule includes a red "🗑️ Delete" button that:
- Shows a confirmation dialog before deletion
- Displays the message content for verification
- Deletes the schedule immediately upon confirmation
- Works the same as the `/delete_schedule` command

### Security Features

- **User Isolation**: You can only view and delete your own schedules
- **Confirmation Dialogs**: Delete buttons require confirmation to prevent accidental deletions
- **Error Handling**: Graceful error messages for invalid IDs or missing schedules
- **AWS Cleanup**: Automatically removes EventBridge rules when schedules are deleted

## Schedule Format Examples

### Recurring Schedules
- `every day at 9am` → Runs daily at 9:00 AM
- `every monday at 2pm` → Runs every Monday at 2:00 PM
- `every friday at 15:45` → Runs every Friday at 3:45 PM

### One-time Schedules
- `2024-12-25 10:00` → Runs once on December 25, 2024 at 10:00 AM
- `2024-06-15 14:30` → Runs once on June 15, 2024 at 2:30 PM

## Technical Details

### Database Storage
Schedules are stored in the `recurring_schedules` table with:
- Unique UUID identifiers
- User and channel association
- Cron expressions for timing
- Active/inactive status
- EventBridge rule ARNs for AWS integration

### AWS Integration
- Creates EventBridge rules for recurring schedules
- Automatically triggers Lambda functions at scheduled times
- Cleans up AWS resources when schedules are deleted
- Handles both local development and production environments

### Error Handling
- Invalid schedule formats show helpful error messages
- Missing schedules return user-friendly notifications
- AWS errors are logged but don't prevent database operations
- Malformed UUIDs are caught and reported clearly

## Best Practices

1. **Use descriptive messages**: Make your scheduled messages clear and actionable
2. **Check your schedules regularly**: Use `/list_schedules` to review active schedules
3. **Clean up unused schedules**: Delete schedules you no longer need to keep things organized
4. **Test timing**: Consider time zones when scheduling (all times are in UTC)
5. **Use appropriate frequency**: Avoid overly frequent schedules that might spam channels

## Troubleshooting

### Common Issues

**"Schedule not found"**
- The schedule may have already been deleted
- Check the schedule ID is correct using `/list_schedules`

**"Invalid schedule ID format"**
- Ensure you're using the full UUID from `/list_schedules`
- UUIDs look like: `8caf5def-c8e3-459c-84ac-826e87476f54`

**"You can only delete your own schedules"**
- You can only manage schedules you created
- Use `/list_schedules` to see your own schedules

**EventBridge errors in logs**
- These are usually non-critical and don't affect schedule deletion
- Contact an administrator if you see persistent AWS errors

## Examples Walkthrough

### Creating and Managing a Daily Report

1. **Create the schedule:**
   ```
   /schedule_msg Generate daily sales report | every day at 8am
   ```

2. **Check it was created:**
   ```
   /list_schedules
   ```

3. **Delete it when no longer needed:**
   ```
   /delete_schedule <schedule_id_from_list>
   ```

### Managing Multiple Schedules

1. **Create several schedules:**
   ```
   /schedule_msg Morning standup | every weekday at 9am
   /schedule_msg Weekly review | every friday at 5pm
   /schedule_msg Monthly report | every first monday at 10am
   ```

2. **Review all schedules:**
   ```
   /list_schedules
   ```

3. **Delete specific ones using buttons or command:**
   - Click the delete button in the list, or
   - Use `/delete_schedule <id>` with the specific ID

This system provides a complete solution for managing automated Slack messages with full lifecycle support from creation to deletion. 