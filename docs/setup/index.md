# Setup Guide

This guide will walk you through all the prerequisites and configurations needed to set up SlackGenie.

## Prerequisites Overview

Before you begin, you'll need:
1. A Slack Workspace with admin access
2. An AWS account with appropriate permissions
3. A Databricks account with Genie access
4. Local development environment setup

## 1. Slack Configuration

### Creating a Slack App

1. Go to [api.slack.com/apps](https://api.slack.com/apps)
2. Click "Create New App"
3. Choose "From scratch"
4. Name your app (e.g., "SlackGenie") and select your workspace
5. Click "Create App"

### Configuring Slack App Permissions

1. In your app's settings, go to "OAuth & Permissions"
2. Under "Scopes", add the following Bot Token Scopes:
   - `chat:write` - To send messages
   - `channels:history` - To read channel messages
   - `channels:join` - To join channels
   - `commands` - To handle slash commands
   - `im:history` - To read direct messages
   - `im:write` - To send direct messages
   - `reactions:write` - To add reactions
   - `users:read` - To read user information

3. Install the app to your workspace
4. Save the following credentials (you'll need them later):
   - Bot User OAuth <PERSON> (starts with `xoxb-`)
   - Signing Secret
   - App ID

### Setting Up Event Subscriptions

1. Go to "Event Subscriptions"
2. Enable events
3. Add your app's URL (will be your AWS API Gateway endpoint)
4. Subscribe to bot events:
   - `message.channels`
   - `message.im`
   - `app_mention`

### Creating Slash Commands

1. Go to "Slash Commands"
2. Create a new command:
   - Command: `/genie`
   - Short Description: "Ask Databricks Genie a question"
   - Usage Hint: "[your question]"
3. Save the command

## 2. AWS Setup

### Required AWS Services

1. Create an IAM user with programmatic access:
   ```shell
   aws configure
   # Enter your:
   # - AWS Access Key ID
   # - AWS Secret Access Key
   # - Default region (e.g., us-east-1)
   # - Default output format (json)
   ```

2. Required AWS services (will be created via Terraform):
   - Lambda (for running the bot)
   - API Gateway (for Slack events)
   - ECR (for Docker images)
   - S3 (for state management)
   - DynamoDB (for state locking)
   - CloudWatch (for logging)

### AWS Permissions

Ensure your IAM user has permissions for:
- Lambda management
- API Gateway management
- ECR management
- S3 management
- DynamoDB management
- CloudWatch management

## 3. Databricks Setup

### Databricks Workspace Configuration

1. Log into your Databricks workspace
2. Ensure you have access to Databricks Genie
3. Generate a Personal Access Token:
   - Go to User Settings
   - Select "Developer Settings"
   - Generate a new token
   - Save the token securely

### Databricks Genie Access

1. Verify Genie access:
   ```python
   import databricks.genie as genie
   
   # Test connection
   genie.test_connection()
   ```

2. Required permissions:
   - Read access to relevant databases
   - Execute permissions for Genie queries
   - Access to Genie API endpoints

## 4. Local Development Environment

### Required Tools

1. Install development tools:
   ```shell
   # macOS
   brew install just
   brew install docker
   brew install terraform
   
   # Python tools
   pip install poetry
   ```

2. Clone the repository:
   ```shell
   git clone https://github.com/yourusername/SlackGenie.git
   cd SlackGenie
   ```

3. Set up environment variables:
   ```shell
   cp .env.sample .env
   ```

4. Edit `.env` with your credentials:
   ```env
   SLACK_BOT_TOKEN=xoxb-your-bot-token
   SLACK_SIGNING_SECRET=your-signing-secret
   SLACK_APP_ID=your-app-id
   DATABRICKS_TOKEN=your-databricks-token
   AWS_ACCESS_KEY_ID=your-aws-access-key
   AWS_SECRET_ACCESS_KEY=your-aws-secret-key
   ```

### Database Setup

1. Start PostgreSQL:
   ```shell
   docker compose up -d postgres
   ```

2. Apply migrations:
   ```shell
   poetry run alembic upgrade head
   ```

## 5. Testing the Setup

1. Start the development server:
   ```shell
   just app-run
   ```

2. Test Slack integration:
   - Invite your bot to a channel
   - Send a test message: `/genie hello`
   - Verify the bot responds

3. Test Databricks integration:
   - Send a test query: `/genie show tables in default`
   - Verify Genie responds with table information

## Troubleshooting

### Common Issues

1. Slack Events Not Receiving:
   - Check API Gateway URL configuration
   - Verify event subscriptions are enabled
   - Check CloudWatch logs for errors

2. Databricks Connection Issues:
   - Verify token permissions
   - Check network access
   - Review Genie API logs

3. AWS Deployment Problems:
   - Check IAM permissions
   - Verify resource limits
   - Review CloudWatch logs

### Getting Help

- Check the [GitHub Issues](https://github.com/yourusername/SlackGenie/issues)
- Review [CloudWatch Logs](https://console.aws.amazon.com/cloudwatch)
- Contact your Databricks administrator for Genie access issues