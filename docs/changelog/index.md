# Changelog

All notable changes to SlackGenie will be documented in this file.

## [Unreleased]

### Added
- Documentation structure with MkDocs Material theme
- Comprehensive setup guide for Slack, AWS, and Databricks integration
- Deployment guide for AWS infrastructure using Terraform
- Installation guide with prerequisites and step-by-step instructions

### Changed
- Improved code organization and structure
- Enhanced documentation styling and structure
- Namespaced app just commands
- Fixed AWS commands
- Added missing docstrings

### Removed
- Legacy files and unused overrides
- Unused dependencies

### Fixed
- Linter errors for CI
- Code formatting issues

## [v0.9.1] - 2024-03-31

### Added
- Return TA feature implementation

## [v0.9.0] - 2024-03-29

### Added
- Initial project setup
- Basic Slack bot integration
- Databricks Genie integration
- Documentation structure

## [0.1.0] - YYYY-MM-DD
- Initial release 