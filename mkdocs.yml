site_name: SlackGenie
site_url: https://mydomain.org/mysite
# Configuration
theme:
  name: material
  custom_dir: overrides/material
  features:
    - announce.dismiss
    - content.action.edit
    - content.action.view
    - content.code.annotate
    - content.code.copy
    - content.code.select
    - content.tooltips
    - navigation.footer
    - navigation.indexes
    - navigation.sections
    - navigation.tabs
    - navigation.top
    - navigation.tracking
    - search.highlight
    - search.share
    - search.suggest
    - toc.follow
  palette:
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/link
        name: Switch to light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: indigo
      accent: indigo
      toggle:
        icon: material/toggle-switch
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: black
      accent: indigo
      toggle:
        icon: material/toggle-switch-off
        name: Switch to system preference
  font:
    text: Roboto
    code: Roboto Mono
  favicon: assets/favicon.png
  icon:
    logo: logo
extra_css:
  - assets/stylesheets/extra.css
extra_javascript:
  - assets/javascripts/extra.js
plugins:
  - git-revision-date-localized:
      type: date
  - awesome-pages:
  - search:
      lang:
        - en
  - include-markdown:
      encoding: utf-8
      preserve_includer_indent: false
      dedent: false
      trailing_newlines: true
      comments: true
      rewrite_relative_urls: true
      heading_offset: 0
      recursive: true
# Page tree
nav:
  - Home: index.md
  - Getting started:
      - setup/index.md
      - Installation:
          - installation/index.md
      - Changelog:
          - changelog/index.md
  - Deployment:
      - deployment/index.md
  - Using SlackGenie:
      - using-slackgenie/index.md
markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - pymdownx.superfences
