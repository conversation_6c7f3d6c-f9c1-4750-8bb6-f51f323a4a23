name: Dagger CI/CD Pipeline

on:
  push:
    branches: [main, master, develop, feat/*, feature/*, fix/*, hotfix/*, chore/*, refactor/*, perf/*, test/*]
  pull_request:
    branches: [main, master, develop, feat/*, feature/*, fix/*, hotfix/*, chore/*, refactor/*, perf/*, test/*]

jobs:
  dagger:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install Dagger CLI
        run: |
          curl -L https://dl.dagger.io/dagger/install.sh | sh
          sudo mv bin/dagger /usr/local/bin
          dagger version

      - name: Install Dagger SDK
        run: pip install dagger-io

      - name: Run Lint on Dagger
        run: dagger call check --source=./.dagger/src
        
      - name: Run Lint on New App
        run: dagger call check --source=./app2
        
      - name: Run Format on Infra code
        run: dagger call check --source=./tests