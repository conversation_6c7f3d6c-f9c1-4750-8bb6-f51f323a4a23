name: Dagger CI/CD Pipeline

on:
  push:
    branches: [ci/*]
  pull_request:
    branches: [ci/*]

jobs:
  dagger:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'

      - name: Install Dagger CLI
        run: |
          curl -L https://dl.dagger.io/dagger/install.sh | sh
          sudo mv bin/dagger /usr/local/bin
          dagger version

      - name: Install Dagger SDK
        run: pip install dagger-io

      - name: Run Lint on Dagger
        run: dagger call test --source=./