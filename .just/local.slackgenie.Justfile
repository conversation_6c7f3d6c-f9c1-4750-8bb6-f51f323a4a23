# Run the application locally for the databricks genie slack bot
run-slackbot:
    # just docker-run-detached-for-service postgres
    poetry run python app2/main.py
    
# Run the application locally with AppMap recording the whole process, when you press Ctrl+C, it will stop and save the recording
run-slackbot-appmap:
    APPMAP_RECORD_PROCESS=true poetry run appmap-python --record process app2/main.py