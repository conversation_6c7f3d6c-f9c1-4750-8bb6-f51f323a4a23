#!/usr/bin/env just --justfile

# Get current version from pyproject.toml
release-get-version:
   uv add --script .scripts/get_version.py toml
   uv run .scripts/get_version.py

# Commit version change, create tag, and push
release-commit-and-tag: release-get-version
    #!/usr/bin/env bash
    VERSION=$(just release-get-version)
    git add pyproject.toml
    git commit -m "chore: update version number"
    git tag -a "v$VERSION" -m "Release version $VERSION"
    git push origin main
    git push upstream main
    git push origin "v$VERSION"
    git push upstream "v$VERSION"    

# Create a release in the upstream repository
release-create-release: release-get-version
    #!/usr/bin/env bash
    VERSION=$(just release-get-version)
    gh release create "v$VERSION" \
        --title "Release v$VERSION" \
        --notes "Release version $VERSION" \
        --repo $(git remote get-url upstream | sed 's/.*github.com[:\/]\(.*\).git/\1/')

# Full release process
release: release-commit-and-tag release-create-release
