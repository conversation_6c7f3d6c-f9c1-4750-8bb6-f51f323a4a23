# Get the current AWS CLI and Terraform CLI
aws-setup: aws-cli-setup aws-terraform-setup

# Install AWS CLI
aws-cli-setup:
    #!/usr/bin/env bash    
    if ! command -v aws &> /dev/null; then
        echo "Installing AWS CLI..."
        curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
        sudo installer -pkg AWSCLIV2.pkg -target /
        echo "AWS CLI installed successfully!"
    else
        echo "AWS CLI is already installed."
    fi

# Install Terraform CLI
aws-terraform-setup:
    #!/usr/bin/env bash    
    if ! command -v terraform &> /dev/null; then
        echo "Installing Terraform CLI..."
        brew install terraform
        echo "Terraform CLI installed successfully!"
    else
        echo "Terraform CLI is already installed."
    fi
    
    
# Configure the AWS CLI Enter the StartURL as: https://d-9067809b5b.awsapps.com/start/# region as: us-east-1, When finished go to https://d-9067809b5b.awsapps.com/start/#/?tab=accounts and configure in ~/.aws/credentials the profile ************_AdministratorAccess with the option Option 2: Add a profile to your AWS credentials file

# Configure the AWS CLI
aws-configure:
    aws configure sso

# Deploy general infra    
aws-init-infra:
    cd terraform && terraform init

# Deploy dev infra    
aws-init_plan-dev-infra:
    cd terraform/environments/dev && terraform init && terraform plan -var-file=dev.tfvars -var-file=secrets.tfvars -out plan.tfplan

# Apply dev infra
aws-apply-dev-infra:
    cd terraform/environments/dev && terraform apply -var-file=dev.tfvars -var-file=secrets.tfvars

# Deploy infra
aws-infra: aws-init-infra aws-init_plan-dev-infra aws-apply-dev-infra

# Login to ECR
aws-login-ecr:
    aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com/slackgenie-ecr

# Build docker image
aws-build-docker-build-lambda2:
    just app-install-only-main
    docker buildx build --pull --platform=linux/amd64 -t ************.dkr.ecr.us-east-1.amazonaws.com/slackgenie-ecr:latest -f Dockerfile.lambda2 .

# Upload docker image
aws-upload-docker-image:
    docker push ************.dkr.ecr.us-east-1.amazonaws.com/slackgenie-ecr:latest

# Deploy dev lambda
aws-deploy-dev-lambda:    
    aws lambda update-function-code \
        --function-name slackgenie-lambda \
        --image-uri ************.dkr.ecr.us-east-1.amazonaws.com/slackgenie-ecr:latest | jq

# Deploy        
aws-deploy2: aws-login-ecr aws-build-docker-build-lambda2 aws-upload-docker-image aws-deploy-dev-lambda

# Invoke dev lambda for app2
aws-lambda-invoke-dev2:
    curl -sSX GET https://distillery-lab.fyi/dev/ | jq
    curl -sSX GET https://distillery-lab.fyi/dev/config | jq
    curl -sSX GET https://distillery-lab.fyi/dev/health | jq
    curl -sSX GET https://distillery-lab.fyi/dev/version | jq

# Invoke dev lambda for app2
aws-lambda-invoke-dev2-open:
    open -a "Google Chrome" "https://distillery-lab.fyi/dev/"
    open -a "Google Chrome" "https://distillery-lab.fyi/dev/docs"
    open -a "Google Chrome" "https://distillery-lab.fyi/dev/redoc"
    open -a "Google Chrome" "https://distillery-lab.fyi/dev/config"
    open -a "Google Chrome" "https://distillery-lab.fyi/dev/version"
    open -a "Google Chrome" "https://distillery-lab.fyi/dev/health"

# Monitor dev lambda
aws-lambda-monitor-dev:
    open -a "Google Chrome" "https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:live-tail$3FlogGroupArns$3D~(~'arn*3aaws*3alogs*3aus-east-1*3a*************3alog-group*3a*2faws*2fapi_gateway*2fslackgenie-api~'arn*3aaws*3alogs*3aus-east-1*3a*************3alog-group*3a*2faws*2flambda*2fslackgenie-lambda)"
    open -a "Google Chrome" "https://us-east-1.console.aws.amazon.com/lambda/home?region=us-east-1#/functions/slackgenie-lambda?subtab=triggers&tab=monitoring"

# Monitor dev lambda logs
aws-lambda-monitor-dev-logs-cli:
    aws logs get-log-events --log-group-name "/aws/lambda/slackgenie-lambda" --log-stream-name $(aws logs describe-log-streams --log-group-name "/aws/lambda/slackgenie-lambda" --order-by LastEventTime --descending --limit 1 --query 'logStreams[0].logStreamName' --output text)

# Get the last modified date of the dev lambda
aws-lambda-last-modified:
    aws lambda get-function-configuration --function-name slackgenie-lambda --region us-east-1 --profile AdministratorAccess-************ | jq .LastModified
    
aws-audit-image:
    # Pull the latest base image to get updated glibc packages
    docker pull public.ecr.aws/lambda/python:3.12-x86_64

    # Build your Docker image
    docker build -f Dockerfile.lambda2 -t slackgenie-lambda-x86_64 .
    
    # Check the values for the installed glibc values
    docker run --rm --entrypoint="" slackgenie-lambda-x86_64 rpm -qa | grep glibc

    # Run Trivy scan to verify vulnerabilities are fixed
    brew install trivy
    trivy image slackgenie-lambda-x86_64