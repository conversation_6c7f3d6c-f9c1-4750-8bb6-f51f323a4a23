# Set up environment by copying sample env file if needed
app-setup-env:
    #!/usr/bin/env bash
    if [ ! -f .env ]; then
        echo "Creating .env file from .env.sample..."
        cp .env.sample .env
        echo "Please update the .env file with your specific configuration."
    else
        echo ".env file already exists."
    fi
    if ! command -v mise &> /dev/null; then
        echo "Mise is not installed. Installing..."
        curl https://mise.run | sh
    fi
    mise install
    mise shell zsh
    mise shell bash
    if ! command -v brew &> /dev/null; then
        echo "Brew is not installed. Installing..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    brew bundle --file=Brewfile    
    
# Export the project as a Python package
app-export-poetry-to-requirements-txt:
    poetry export --without-hashes --output=requirements.txt --format=requirements.txt
    cp requirements.txt app2/requirements.txt

# Build the project
app-build: app-export-poetry-to-requirements-txt
    poetry install --only-root
    just app-export-poetry-to-requirements-txt
    poetry build

# Install Python dependencies
app-install: app-setup-env
    poetry config virtualenvs.create true
    poetry config virtualenvs.in-project true
    poetry env use python3.12
    poetry env activate
    poetry install --with dev,docs,test,jupyter,web
    just app-export-poetry-to-requirements-txt
    
app-install-only-main: app-setup-env
    poetry sync --only main
    just app-export-poetry-to-requirements-txt
    
# Run the application locally for the databricks genie slack bot
app-run: app-install app-export-poetry-to-requirements-txt run-slackbot

# Check the application is running
app-check:
    curl -s "http://localhost:5000/health" | jq
    curl -s "http://localhost:5000/version" | jq
    curl -s "http://localhost:5000/config"  | jq 

# Run tests
app-test:
    just docker-run-detached-for-service postgres
    poetry run pytest
    
# Set up everything for local development
app-setup: app-setup-env app-install
    @echo "Local development environment is ready!"

# Format code
app-format:
    ruff format app2/
    ruff format .dagger/src/slack_genie/
    ruff format tests/

# Run linters
app-lint:
    ruff check app2/ --fix
    ruff check .dagger/src/slack_genie/ --fix
    ruff check tests/ --fix

# Fix formatting and linting
app-fix: app-format app-lint