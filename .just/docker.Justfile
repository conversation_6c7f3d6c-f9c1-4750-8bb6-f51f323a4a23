# Build Docker image
docker-build: app-export-poetry-to-requirements-txt
    docker-compose build --no-cache

# Run the application in Docker
docker-run: app-export-poetry-to-requirements-txt
    docker-compose up --build

# Run the application in Docker detached mode
docker-run-detached: app-export-poetry-to-requirements-txt
    docker-compose up -d --build

# Run the application in Docker detached mode for a specific service
docker-run-detached-for-service service="": app-export-poetry-to-requirements-txt
    docker-compose up {{service}} -d

# Stop Docker containers
docker-stop: 
    docker-compose down

# Show Docker logs
docker-logs:
    docker-compose logs -f

# Execute a command in the running container
docker-exec command="bash":
    docker-compose exec app {{command}}

# Rebuild and restart services
docker-restart: docker-stop docker-build docker-run

# Run custom docker cleanup script
docker-clean:
    #!/usr/bin/env zsh
    chmod +x .scripts/clean-docker.sh
    ./.scripts/clean-docker.sh
    echo "Docker cleanup completed using custom script"

# Complete reset - stops containers, removes volumes, rebuilds, and restarts
docker-reset: docker-stop docker-clean docker-build docker-run