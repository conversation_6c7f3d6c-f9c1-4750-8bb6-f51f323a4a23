# Install Dagger CLI if not already installed
dagger-install:
    #!/usr/bin/env bash
    if ! command -v dagger &> /dev/null; then
        echo "Installing Dagger CLI..."
        curl -L https://dl.dagger.io/dagger/install.sh | sh
        echo "Dagger CLI installed successfully!"
    else
        echo "Dagger CLI is already installed."
    fi

# Reset Dagger
dagger-reset:
    chmod +x .scripts/dagger-reset.sh
    ./.scripts/dagger-reset.sh
    
dagger-run:
    dagger call check --source=./app2
    dagger call check --source=./tests
    dagger call check --source=./.dagger/src
    dagger call test --source=./