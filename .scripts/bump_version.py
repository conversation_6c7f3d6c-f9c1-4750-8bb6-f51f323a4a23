#!/usr/bin/env python3
# /// script
# requires-python = ">=3.12"
# dependencies = [
#     "toml",
# ]
# ///
import re
import subprocess
import sys

import toml


def get_latest_version_tag():
    # Get all tags sorted by version (assuming semantic versioning)
    result = subprocess.run(
        ["git", "tag", "-l", "v*"],
        capture_output=True,
        text=True,
    )
    tags = result.stdout.strip().splitlines()

    # Filter tags that match semantic versioning pattern (vX.Y.Z)
    version_pattern = re.compile(r"^v\d+\.\d+\.\d+$")
    version_tags = [tag for tag in tags if version_pattern.match(tag)]

    if not version_tags:
        return None

    # Sort tags by version components
    def version_key(tag):
        # Remove 'v' prefix and split into components
        components = tag[1:].split(".")
        return tuple(map(int, components))

    return sorted(version_tags, key=version_key)[-1]


# Read current version
with open("pyproject.toml") as f:
    config = toml.load(f)

current_version = config["tool"]["poetry"]["version"]
major, minor, patch = map(int, current_version.split("."))

# Get version bump type from command line argument, default to 'minor'
type = "minor" if len(sys.argv) < 2 else sys.argv[1].lower()

# Validate version bump type
if type not in ["major", "minor", "patch", "auto"]:
    print(f"Invalid version bump type: {type}")
    print("Valid types are: major, minor, patch, auto")
    sys.exit(1)

# Check for fix commits if type is auto
if type == "auto":
    latest_tag = get_latest_version_tag()
    if latest_tag:
        # Get commits since the latest version tag
        result = subprocess.run(
            ["git", "log", "--format=%s", f"{latest_tag}..HEAD"],
            capture_output=True,
            text=True,
        )
    else:
        # If no version tags exist, get all commits
        result = subprocess.run(
            ["git", "log", "--format=%s"],
            capture_output=True,
            text=True,
        )

    commits = result.stdout.splitlines()
    has_fixes = any(commit.startswith("fix:") for commit in commits)
    type = "patch" if has_fixes else "minor"

# Bump version according to type
if type == "patch":
    patch += 1
elif type == "minor":
    minor += 1
    patch = 0
elif type == "major":
    major += 1
    minor = 0
    patch = 0

new_version = f"{major}.{minor}.{patch}"
config["tool"]["poetry"]["version"] = new_version

# Write back to file
with open("pyproject.toml", "w") as f:
    toml.dump(config, f)

print(f"Version bumped from {current_version} to {new_version}")
