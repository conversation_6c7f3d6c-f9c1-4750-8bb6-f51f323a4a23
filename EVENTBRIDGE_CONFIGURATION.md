# EventBridge Configuration for /schedule_msg Command

## Problem
The `/schedule_msg` command was returning "local-development-rule" even when deployed in AWS Lambda because the EventBridge service couldn't find the required AWS configuration values.

## Root Cause
The EventBridge service requires two ARN values that were not configured in AWS Secrets Manager:
1. **`LAMBDA_ARN`** - The ARN of your Lambda function
2. **`EVENTBRIDGE_ROLE_ARN`** - The ARN of the IAM role that EventBridge uses to invoke Lambda

## Solution

### Step 1: Get Your Lambda Function ARN
You can find your Lambda function ARN in the AWS Console or using AWS CLI:

```bash
# Using AWS CLI
aws lambda get-function --function-name your-function-name --query 'Configuration.FunctionArn'
```

The ARN will look like:
```
arn:aws:lambda:us-east-1:989284569019:function:your-function-name
```

### Step 2: Create EventBridge IAM Role
You need an IAM role that allows EventBridge to invoke your Lambda function.

**IAM Role Trust Policy** (allows <PERSON><PERSON><PERSON> to assume the role):
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "events.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
```

**IAM Role Permission Policy** (allows invoking your Lambda):
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": "lambda:InvokeFunction",
      "Resource": "arn:aws:lambda:us-east-1:989284569019:function:your-function-name"
    }
  ]
}
```

The role ARN will look like:
```
arn:aws:iam::989284569019:role/EventBridgeInvokeLambdaRole
```

### Step 3: Update AWS Secrets Manager
Use the provided script to add these ARNs to your AWS Secrets Manager:

```bash
# First, do a dry run to see what will be updated
python scripts/update_aws_secrets.py \
    --lambda-arn "arn:aws:lambda:us-east-1:989284569019:function:your-function-name" \
    --eventbridge-role-arn "arn:aws:iam::989284569019:role/EventBridgeInvokeLambdaRole" \
    --dry-run

# If everything looks good, run without --dry-run
python scripts/update_aws_secrets.py \
    --lambda-arn "arn:aws:lambda:us-east-1:989284569019:function:your-function-name" \
    --eventbridge-role-arn "arn:aws:iam::989284569019:role/EventBridgeInvokeLambdaRole"
```

### Step 4: Update Lambda IAM Role
Your Lambda function's IAM role needs permissions to:
1. **Create EventBridge rules** - `events:PutRule`, `events:PutTargets`
2. **Delete EventBridge rules** - `events:DeleteRule`, `events:RemoveTargets`
3. **List EventBridge rules** - `events:ListRules`

Add this policy to your Lambda's IAM role:
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "events:PutRule",
        "events:PutTargets",
        "events:DeleteRule",
        "events:RemoveTargets",
        "events:ListRules"
      ],
      "Resource": "*"
    }
  ]
}
```

### Step 5: Redeploy Lambda Function
After updating the secrets, redeploy your Lambda function so it picks up the new configuration.

## How It Works

### Environment Detection
The EventBridge service now properly detects the environment:

- **In Lambda**: Uses IAM role credentials automatically, only requires ARN configuration
- **In Local Development**: Requires explicit AWS credentials + ARN configuration

### Configuration Validation
The service checks for required values based on environment:

**Lambda Environment:**
- ✅ `LAMBDA_ARN` (from AWS Secrets Manager)
- ✅ `EVENTBRIDGE_ROLE_ARN` (from AWS Secrets Manager)
- ✅ AWS credentials (automatic via IAM role)

**Local Development:**
- ✅ `LAMBDA_ARN` (from .env file)
- ✅ `EVENTBRIDGE_ROLE_ARN` (from .env file)
- ✅ `AWS_ACCESS_KEY_ID` (from .env file)
- ✅ `AWS_SECRET_ACCESS_KEY` (from .env file)

## Testing

### Test in Lambda
After configuration, the `/schedule_msg` command should:
1. Create actual EventBridge rules (not "local-development-rule")
2. Return the real EventBridge rule ARN
3. Successfully schedule recurring messages

### Test Logs
Look for these log messages in CloudWatch:
```
Running in Lambda environment with IAM role credentials
AWS Configuration - Region: us-east-1, Lambda ARN configured: Yes, Role ARN configured: Yes
Successfully connected to AWS EventBridge using IAM role
Creating EventBridge rule: slack-schedule-{id}
Successfully created EventBridge rule: slack-schedule-{id}
```

## Troubleshooting

### "local-development-rule" Still Returned
- Check that both ARNs are in AWS Secrets Manager
- Verify Lambda function has been redeployed after updating secrets
- Check CloudWatch logs for configuration errors

### EventBridge Rule Creation Fails
- Verify Lambda IAM role has EventBridge permissions
- Check that EventBridge role ARN is correct and exists
- Ensure EventBridge role has permission to invoke your Lambda

### Permission Denied Errors
- Verify EventBridge role trust policy allows `events.amazonaws.com`
- Check that EventBridge role has `lambda:InvokeFunction` permission
- Ensure Lambda role has EventBridge management permissions

## Security Notes
- EventBridge role should only have permission to invoke your specific Lambda function
- Lambda role should have minimal EventBridge permissions (only what's needed)
- ARNs in Secrets Manager are masked in logs for security 