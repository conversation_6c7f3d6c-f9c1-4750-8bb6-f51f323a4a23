# Ignore node_modules directory (for Node.js projects)
node_modules/

# Ignore Python virtual environment directories
venv/
env/

# Ignore compiled Python files
__pycache__/
*.py[cod]

# Ignore macOS system files
.DS_Store

# Ignore build directories and files (for various projects)
build/
dist/
*.o
*.obj
*.class
*.exe
*.dll
*.so

# Ignore log files
*.log

# Ignore IDE specific files
.vscode/
.idea/

# Ignore system files
Thumbs.db

# Ignore Torch large files
venv/Lib/site-packages/torch/lib/dnnl.lib
venv/Lib/site-packages/torch/lib/torch_cpu.dll

flagged
out.tar/
.venv

__pycache__
.env
tmp

# Never ignore .gitignore
!/.gitignore

# Terraform
.terraform/
*.tfstate
*.tfstate.backup
secrets.tfvars
prod.tfvars
!*.example.tfvars
!*.template.tfvars
terraform.tfvars.json
override.tf
override.tf.json
*_override.tf
*_override.tf.json
.terraformrc
terraform.rc
*.pkg
terraform/environments/dev/plan.tfplan

temp-credentials.json
site
*.env

.next/
/app2/frontend

terraform/modules/budget_notifications/budget_notifications.zip
