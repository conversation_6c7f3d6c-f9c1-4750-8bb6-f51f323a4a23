# Deployment Guide for SlackGenie Infrastructure

This guide provides instructions for deploying the SlackGenie application to AWS using Terraform.

## Prerequisites

- AWS CLI installed and configured
- Terraform CLI installed (v1.0.0+)
- Docker installed
- Git installed

## Repository Structure

The infrastructure is organized as follows:

```
terraform/
├── modules/                   # Reusable Terraform modules
│   ├── api_gateway/           # API Gateway configuration
│   ├── cicd/                  # CI/CD pipeline with dagger.io integration
│   ├── ecr/                   # ECR repository for Docker images
│   ├── lambda/                # Lambda function configuration
│   └── tags/                  # Tagging module for cost tracking
├── environments/              # Environment-specific configurations
│   ├── dev/                   # Development environment
│   │   ├── main.tf           # Main Terraform configuration
│   │   ├── variables.tf      # Variables definition
│   │   ├── outputs.tf        # Outputs definition
│   │   ├── dev.tfvars        # Non-sensitive variables for dev
│   │   └── secrets.tfvars    # Sensitive variables (not in git)
│   └── prod/                  # Production environment
│       ├── main.tf           # Main Terraform configuration
│       ├── variables.tf      # Variables definition
│       ├── outputs.tf        # Outputs definition
│       ├── prod.tfvars       # Non-sensitive variables for prod
│       └── secrets.tfvars    # Sensitive variables (not in git)
├── backend.tf                 # Remote state configuration
├── bootstrap.tf               # State backend resources
└── .gitignore                 # Git ignore file for secrets
```

## Setting Up Secrets

Before deploying, you need to configure the secrets for each environment:

1. Copy the `secrets.tfvars.template` file (if available) to `secrets.tfvars` in each environment directory
2. Fill in the sensitive values in the `secrets.tfvars` files
3. Ensure these files are not committed to Git (.gitignore should handle this)

Example `secrets.tfvars` structure:

```hcl
# Sensitive variables
github_repo_url = "https://github.com/your-org/slackgenie"
alarm_notification_arn = "arn:aws:sns:us-east-1:123456789012:slackgenie-alerts"

# Lambda environment secrets
lambda_environment_secrets = {
  "API_KEY" = "your-api-key"
  "DB_PASSWORD" = "your-database-password"
}

# If using VPC, add actual subnet and security group IDs
subnet_ids = ["subnet-xxxxxxxxxx", "subnet-yyyyyyyyyy"]
security_group_ids = ["sg-xxxxxxxxxx"]
```

## Deployment Process

### Step 1: Initialize the Terraform Backend (First Time Only)

The first time you deploy, you need to set up the S3 bucket and DynamoDB table for state management:

```shell
# Navigate to the terraform directory
cd terraform

# Initialize Terraform (local state)
terraform init

# Create the state backend resources
terraform apply -target=aws_s3_bucket.terraform_state -target=aws_dynamodb_table.terraform_locks
```

### Step 2: Deploy to Development Environment

```shell
# Navigate to the dev environment
cd environments/dev

# Initialize Terraform with remote state
terraform init

# Validate the configuration
terraform validate

# Plan the deployment (including secrets)
terraform plan -var-file=dev.tfvars -var-file=secrets.tfvars

# Apply the configuration
terraform apply -var-file=dev.tfvars -var-file=secrets.tfvars
```

### Step 3: Push Docker Image to ECR

After the ECR repository is created, build and push the Docker image:

```shell
# Get the ECR repository URL from output
ECR_REPO=$(terraform output -raw ecr_repository_url)

# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REPO

# Build the Docker image
docker build -t $ECR_REPO:latest -f ../../../Dockerfile.lambda ../../../

# Push the image to ECR
docker push $ECR_REPO:latest
```

### Step 4: Deploy to Production Environment

Once development deployment is tested and approved:

```shell
# Navigate to the prod environment
cd ../prod

# Initialize Terraform with remote state
terraform init

# Plan the production deployment
terraform plan -var-file=prod.tfvars -var-file=secrets.tfvars

# Apply the configuration
terraform apply -var-file=prod.tfvars -var-file=secrets.tfvars
```

### Step 5: Push Docker Image to Production ECR

```shell
# Get the production ECR repository URL
ECR_REPO=$(terraform output -raw ecr_repository_url)

# Login to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin $ECR_REPO

# Build the Docker image
docker build -t $ECR_REPO:latest -f ../../../Dockerfile.lambda ../../../

# Push the image to ECR
docker push $ECR_REPO:latest
```

## Incremental Deployment

You can deploy components incrementally using targeted applies:

```shell
# Deploy just the ECR repository
terraform apply -var-file=dev.tfvars -var-file=secrets.tfvars -target=module.ecr

# Deploy just the Lambda function
terraform apply -var-file=dev.tfvars -var-file=secrets.tfvars -target=module.lambda

# Deploy just the API Gateway
terraform apply -var-file=dev.tfvars -var-file=secrets.tfvars -target=module.api_gateway

# Deploy just the CI/CD pipeline
terraform apply -var-file=dev.tfvars -var-file=secrets.tfvars -target=module.cicd
```

## Cost Tracking

All resources are tagged for cost tracking. To view costs by environment or project:

1. Go to AWS Cost Explorer
2. Group by the "Environment" tag to see costs by environment
3. Group by the "Project" tag to see costs for SlackGenie vs other projects
4. Group by the "CostCenter" tag for departmental billing

## Cleanup

To tear down the infrastructure:

```shell
# Navigate to the environment directory
cd environments/dev

# Destroy the resources
terraform destroy -var-file=dev.tfvars -var-file=secrets.tfvars
```

## Troubleshooting

- **State Issues**: If you encounter state issues, check the S3 bucket and DynamoDB table
- **Permission Issues**: Ensure your AWS credentials have the necessary permissions
- **Docker Push Failures**: Check your ECR permissions and ensure you're logged in
- **Lambda Errors**: Check CloudWatch logs under /aws/lambda/slackgenie-lambda-{environment}
- **API Gateway Errors**: Check CloudWatch logs under /aws/api_gateway/slackgenie-api-{environment} 