# Git Workflow for SlackGenie Infrastructure

This document outlines the Git workflow for managing the SlackGenie infrastructure code.

## Current Branch Structure

- `main`: Main branch containing the production-ready code
- `feat/infra`: Feature branch containing infrastructure code developed with Terraform

## Working with the Feature Branch

### 1. Initial Setup

If you haven't already created the feature branch:

```shell
# Ensure you're on the main branch
git checkout main

# Create and switch to the feature branch
git checkout -b feat/infra
```

### 2. Committing Changes

After making changes to the Terraform infrastructure code:

```shell
# Check what files have been modified
git status

# Add all Terraform files (excluding secrets)
git add terraform/modules/ terraform/environments/ terraform/backend.tf terraform/bootstrap.tf terraform/README.md terraform/DEPLOYMENT.md terraform/GIT.md terraform/*tfvars.template Dockerfile.lambda

# Create a meaningful commit
git commit -m "feat: add AWS Lambda infrastructure with tagging support"
```

### 3. Important Files to Exclude

Ensure the following files are NOT committed to Git:

- `secrets.tfvars` files containing sensitive information
- `.terraform/` directories
- `terraform.tfstate` and state backup files
- `terraform.tfvars.json` files

The `.gitignore` file is set up to exclude these patterns.

### 4. Pushing to Remote

To push your changes to the remote repository:

```shell
# Push the feature branch to origin
git push -u origin feat/infra
```

### 5. Creating a Pull Request

Once the infrastructure code is ready for review:

1. Go to the GitHub repository
2. Create a pull request from `feat/infra` to `main`
3. Add a description explaining the infrastructure changes
4. Request reviews from team members

### 6. Applying Changes from the Feature Branch

While the code is still in a feature branch, you can still apply the Terraform configuration:

```shell
# Clone the repository
git clone https://github.com/your-organization/slackgenie.git
cd slackgenie

# Switch to the feat/infra branch
git checkout feat/infra

# Follow the deployment instructions in DEPLOYMENT.md
cd terraform
# ... continue with deployment steps
```

### 7. Keeping the Branch Updated

If the main branch has been updated, keep your feature branch up to date:

```shell
# Get the latest changes from main
git checkout main
git pull

# Update your feature branch
git checkout feat/infra
git merge main

# Resolve any conflicts if needed
```

## Best Practices

1. **Commit Messages**: Use conventional commit format:
   - `feat: add new infrastructure component`
   - `fix: resolve issue with IAM permissions`
   - `docs: update deployment instructions`

2. **Granular Commits**: Make small, focused commits that are easy to review

3. **Review Terraform Plans**: Always run `terraform plan` before applying changes, and share the plan output in pull requests

4. **Branch Protection**: Enable branch protection rules for the main branch to require reviews and passing checks

5. **Secrets Management**: Never commit sensitive information to Git. Use `secrets.tfvars` for local development and consider AWS Secrets Manager or SSM Parameter Store for production secrets

By following these guidelines, we can maintain a clean and organized infrastructure codebase. 