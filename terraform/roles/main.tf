data "aws_caller_identity" "current" {}

provider "aws" {
  region = "us-east-1"
}

resource "aws_iam_role" "terraform_role" {
  name = "terraform-deployment-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::989284569019:root"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "terraform_policy" {
  name = "terraform-deployment-policy"
  role = aws_iam_role.terraform_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          # ECR
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          # S3
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket",
          "s3:DeleteObject",
          # Lambda
          "lambda:UpdateFunctionCode",
          "lambda:UpdateFunctionConfiguration",
          "lambda:GetFunction",
          # API Gateway
          "apigateway:PUT",
          "apigateway:POST",
          "apigateway:GET",
          "apigateway:DELETE",
          # CloudWatch Logs
          "logs:CreateLogGroup",
          "logs:PutLogEvents"
        ]
        Resource = "*"
      }
    ]
  })
}
