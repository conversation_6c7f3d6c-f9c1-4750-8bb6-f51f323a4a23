# SlackGenie AWS Lambda Infrastructure

This directory contains the Terraform configuration for deploying SlackGenie as a serverless application on AWS Lambda with API Gateway.

## Architecture

The solution uses the following AWS services:

- **Lambda**: Hosts the FastAPI application
- **API Gateway**: Exposes the FastAPI endpoints
- **ECR**: Stores the Docker container image
- **CloudWatch**: Monitors the Lambda function
- **WAF**: Provides security for the API Gateway
- **S3**: Stores the Terraform state
- **DynamoDB**: Provides locking for Terraform state
- **CodeBuild**: Provides CI/CD capabilities with dagger.io integration

## Prerequisites

- AWS CLI configured with appropriate credentials
- Terraform installed (v1.0.0 or newer)
- Docker installed

## Deployment Instructions

### 1. Bootstrap Terraform State Backend

Before deploying the application, you need to set up the S3 bucket and DynamoDB table for Terraform state:

```shell
cd terraform
terraform init
terraform apply -target=aws_s3_bucket.terraform_state -target=aws_dynamodb_table.terraform_locks
```

### 2. Configure Tagging (Optional)

You can customize the resource tags by editing the tag variables in `environments/dev/variables.tf`. By default, the following tags are applied to all resources:

- `Environment`: dev (or prod, staging, etc.)
- `Project`: SlackGenie
- `Owner`: DevOps
- `CostCenter`: Engineering
- `Team`: DevOps
- `ManagedBy`: terraform

### 3. Build and Push the Docker Image

Build the Docker image for the Lambda function:

```shell
cd ..
docker build -t slackgenie -f Dockerfile.lambda .
```

### 4. Deploy the Infrastructure

Initialize and apply the Terraform configuration:

```shell
cd terraform/environments/dev
terraform init
terraform apply
```

After the ECR repository is created, push the Docker image to ECR using the commands from the `push_commands` output.

### 5. Incremental Deployment

You can deploy parts of the infrastructure independently by targeting specific modules:

```shell
# Deploy only the ECR repository
terraform apply -target=module.ecr

# Deploy only the Lambda function
terraform apply -target=module.lambda

# Deploy only the API Gateway
terraform apply -target=module.api_gateway

# Deploy only the CI/CD pipeline
terraform apply -target=module.cicd
```

## Cost Tracking

All resources are tagged with a consistent tagging strategy to facilitate cost tracking and allocation. You can use AWS Cost Explorer to analyze costs by:

- Environment
- Project
- Cost Center
- Team
- Owner

To view costs by tag in AWS Cost Explorer:
1. Activate the user-defined cost allocation tags in the AWS Billing console
2. Wait 24 hours for tags to appear in Cost Explorer
3. Use the "Group by" feature in Cost Explorer to analyze costs by your chosen tags

## Environments

The infrastructure is organized in environments:

- `dev`: Development environment
- `prod`: Production environment (copy and modify from dev)

## Security Features

- WAF for API Gateway protection
- Encrypted ECR repository
- Proper IAM roles with least privilege
- CloudWatch monitoring and alarms
- VPC support for Lambda (optional)

## CI/CD Pipeline

The infrastructure includes an optional CI/CD pipeline using AWS CodeBuild with dagger.io integration:

- Automatically triggered by GitHub webhook when code is pushed
- Runs linting and tests using dagger.io
- Builds and pushes Docker image to ECR
- Updates Lambda function with the new image

To enable the CI/CD pipeline, set the `github_repo_url` variable.

## Cleanup

To remove all resources:

```shell
terraform destroy
```

## Troubleshooting

- Check CloudWatch logs for Lambda function issues
- Verify API Gateway configuration
- Ensure Docker image is built correctly
- Check IAM permissions 