# Template for sensitive information
# Copy this file to secrets.tfvars and fill in the values
# DO NOT commit secrets.tfvars to version control

# GitHub repository settings (if using CI/CD)
github_repo_url = "https://github.com/your-organization/slackgenie"

# SNS topic for alarm notifications
alarm_notification_arn = "arn:aws:sns:us-east-1:123456789012:slackgenie-alerts-dev"

# If using VPC, add actual subnet and security group IDs
subnet_ids = [
  "subnet-xxxxxxxxxxxxxxxxx",
  "subnet-yyyyyyyyyyyyyyyyy"
]
security_group_ids = ["sg-xxxxxxxxxxxxxxxxx"]

# Lambda environment secrets
lambda_environment_secrets = {
  "API_KEY" = "your-api-key-here"
  "DB_PASSWORD" = "your-database-password"
  # Add other sensitive environment variables as needed
} 