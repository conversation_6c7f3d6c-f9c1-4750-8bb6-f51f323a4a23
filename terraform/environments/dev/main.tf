provider "aws" {
  region = var.aws_region
}

# Tags module for resource tagging
module "tags" {
  source = "../../modules/tags"

  environment     = var.environment
  project         = var.project
  owner           = var.owner
  cost_center     = var.cost_center
  team            = var.team
  additional_tags = var.additional_tags
}

# ECR repository for FastAPI application
module "ecr" {
  source = "../../modules/ecr"

  repository_name   = "${var.app_name}-ecr"
  image_count_limit = 10
  allowed_principals = [
    data.aws_caller_identity.current.arn
  ]

  tags = module.tags.tags
}

# Lambda function for FastAPI application
module "lambda" {
  source = "../../modules/lambda"

  function_name = "${var.app_name}-lambda"
  # Change back to using your ECR image
  image_uri     = "${module.ecr.repository_url}:latest"

  memory_size = 2048  # Increase memory to 2GB for faster initialization
  timeout     = 900   # Increase timeout to 15 minutes

  environment_variables = merge({
    PYTHONPATH                 = "/var/task"
    ENVIRONMENT                = var.environment
    LOG_LEVEL                  = "INFO"
    CORS_ORIGINS               = join(",", var.cors_origins)
    DATABASE_URL               = "postgresql://postgres:${var.db_password}@${module.postgres.db_instance_endpoint}/demo_db"
    STAGE_NAME                 = var.stage_name  # Add stage name for API Gateway
  }, var.lambda_environment_secrets)

  # Use VPC for Lambda if configured
  subnet_ids         = var.use_vpc ? var.subnet_ids : null
  security_group_ids = var.use_vpc ? [aws_security_group.lambda.id] : null

  log_retention_days = 365

  tags = module.tags.tags
}

# API Gateway for FastAPI application
module "api_gateway" {
  source = "../../modules/api_gateway"

  api_name             = "${var.app_name}-api"
  stage_name           = var.stage_name
  lambda_function_name = module.lambda.lambda_function_name
  lambda_invoke_arn    = module.lambda.lambda_invoke_arn

  cors_allow_origins     = var.cors_origins
  cors_allow_credentials = false

  throttling_burst_limit = 100
  throttling_rate_limit  = 200

  enable_waf = false

  log_retention_days = 365

  tags = module.tags.tags
}

# CI/CD pipeline (optional)
module "cicd" {
  source = "../../modules/cicd"
  count  = var.github_repo_url != "" ? 1 : 0

  app_name             = var.app_name
  ecr_repository_url   = module.ecr.repository_url
  lambda_function_name = module.lambda.lambda_function_name
  lambda_function_arn  = module.lambda.lambda_function_arn
  github_repo_url      = var.github_repo_url
  github_webhook_enabled = var.github_webhook_enabled
  github_branch        = var.github_branch

  tags = module.tags.tags
}

# Additional resources (if needed)
# CloudWatch alarms for monitoring
resource "aws_cloudwatch_metric_alarm" "lambda_errors" {
  alarm_name          = "${var.app_name}-lambda-errors"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = 60
  statistic           = "Sum"
  threshold           = 5
  alarm_description   = "This metric monitors Lambda function errors"

  dimensions = {
    FunctionName = module.lambda.lambda_function_name
  }

  alarm_actions = var.alarm_notification_arn != "" ? [var.alarm_notification_arn] : []

  tags = module.tags.tags
}

# Get current account ID
data "aws_caller_identity" "current" {}

# Security Group for Lambda function
resource "aws_security_group" "lambda" {
  name        = "${var.app_name}-lambda-sg"
  description = "Security group for Lambda function to access PostgreSQL and AWS services"
  vpc_id      = "vpc-1daa4478"  # Default VPC where RDS is located

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(module.tags.tags, {
    Name = "${var.app_name}-lambda-sg"
  })
}

# AWS Budget Notifications to Slack
module "budget_notifications" {
  source = "../../modules/budget_notifications"

  project_name         = var.project
  environment          = var.environment
  account_budget_amount = var.aws_budget_amount
  threshold_percent    = var.budget_threshold_percent
  email_subscribers    = var.budget_email_subscribers
  ssm_parameter_name   = var.ssm_parameter_name
  log_retention_days   = 30

  # Enable project-specific budgets if configured
  enable_project_budgets = var.enable_project_budgets
  project_budgets        = var.project_budgets

  # Email notifications already set above

  # Apply the same tags as other resources
  tags = module.tags.tags
}

# Agregar módulo de CloudWatch para monitoreo de DynamoDB
module "dynamodb_alarms" {
  source = "../../modules/cloudwatch"

  resource_name = "slackgenie-terraform-locks"
  alarm_prefix  = var.app_name

  # Configuración de umbrales
  read_capacity_threshold  = 80
  write_capacity_threshold = 80

  # Aplicar los mismos tags que otros recursos
  tags = module.tags.tags
}

# Cost-Optimized RDS Setup with Working-Hours Scheduling
module "postgres" {
  source  = "terraform-aws-modules/rds/aws"
  version = "~> 5.0"

  identifier = "${var.app_name}-db"

  # Cost optimizations
  engine               = "postgres"
  engine_version       = "13"
  family               = "postgres13"
  instance_class       = "db.t4g.micro"  # Cheapest option, uses ARM architecture
  allocated_storage    = 20              # Minimum storage

  # Login credentials
  db_name     = "demo_db"
  username    = "postgres"
  password    = var.db_password         # From secrets.tfvars
  port        = 5432

  # Cost savings
  multi_az               = false        # No redundancy for dev
  backup_retention_period = 1           # Minimum backup retention
  skip_final_snapshot    = true         # No final snapshot when deleted
  deletion_protection    = false        # Allow deletion

  # Networking - make sure the database is in the same VPC as Lambda if VPC is enabled
  publicly_accessible    = true         # For easier development access

  # Open PostgreSQL port to all traffic (for development only)
  vpc_security_group_ids = [aws_security_group.postgres.id]

  # Monitoring - minimal
  monitoring_interval    = 0            # Disable enhanced monitoring
  performance_insights_enabled = false  # Disable performance insights

  # Schedule daily stops/starts
  apply_immediately      = true

  tags = module.tags.tags
}

# Security Group for RDS PostgreSQL (dev only - allows connections from restricted IPs only)
resource "aws_security_group" "postgres" {
  name        = "${var.app_name}-postgres-sg"
  description = "Allow PostgreSQL traffic from anywhere (development use only)"

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
    description = "Axels IP"
  }

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["**************/32"]
    description = "Fran IP"
  }

  ingress {
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["***************/32"]
    description = "Hectors IP"
  }

  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [aws_security_group.lambda.id]
    description     = "Allow access from Lambda security group"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(module.tags.tags, {
    Name = "${var.app_name}-postgres-sg"
  })
}

# CloudWatch Alarm for RDS CPU monitoring (without SNS notifications)
resource "aws_cloudwatch_metric_alarm" "rds_cpu_alarm" {
  alarm_name          = "${var.app_name}-db-cpu-alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = 300
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "This metric monitors RDS CPU utilization"
  treat_missing_data  = "notBreaching"

  dimensions = {
    DBInstanceIdentifier = "${var.app_name}-db"
  }

  # No alarm_actions specified - this creates an alarm without notifications
  # alarm_actions = []

  tags = module.tags.tags
}

# CloudWatch Alarm for RDS Freeable Memory monitoring (without SNS notifications)
resource "aws_cloudwatch_metric_alarm" "rds_memory_alarm" {
  alarm_name          = "${var.app_name}-db-memory-alarm"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 2
  metric_name         = "FreeableMemory"
  namespace           = "AWS/RDS"
  period              = 300
  statistic           = "Average"
  threshold           = 100000000  # 100MB en bytes
  alarm_description   = "This metric monitors RDS freeable memory"
  treat_missing_data  = "notBreaching"

  dimensions = {
    DBInstanceIdentifier = "${var.app_name}-db"
  }

  tags = module.tags.tags
}

# CloudWatch Alarm for RDS IO monitoring (without SNS notifications)
resource "aws_cloudwatch_metric_alarm" "rds_io_alarm" {
  alarm_name          = "${var.app_name}-db-io-alarm"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "DiskQueueDepth"
  namespace           = "AWS/RDS"
  period              = 300
  statistic           = "Average"
  threshold           = 10
  alarm_description   = "This metric monitors RDS disk queue depth (IO performance)"
  treat_missing_data  = "notBreaching"

  dimensions = {
    DBInstanceIdentifier = "${var.app_name}-db"
  }

  tags = module.tags.tags
}

# CloudWatch Alarm for RDS Free Storage Space monitoring (without SNS notifications)
resource "aws_cloudwatch_metric_alarm" "rds_storage_alarm" {
  alarm_name          = "${var.app_name}-db-storage-alarm"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 2
  metric_name         = "FreeStorageSpace"
  namespace           = "AWS/RDS"
  period              = 300
  statistic           = "Average"
  threshold           = 2000000000  # 2GB en bytes
  alarm_description   = "This metric monitors RDS free storage space"
  treat_missing_data  = "notBreaching"

  dimensions = {
    DBInstanceIdentifier = "${var.app_name}-db"
  }

  tags = module.tags.tags
}

# Output the RDS connection details
output "database_endpoint" {
  value = module.postgres.db_instance_endpoint
  description = "The database connection endpoint"
}

output "database_connection_string" {
  value = "postgresql://${module.postgres.db_instance_username}:${module.postgres.db_instance_password}@${module.postgres.db_instance_endpoint}/${module.postgres.db_instance_name}"
  description = "The database connection string"
  sensitive = true
}

# Comment out or remove these resources temporarily:
# aws_cloudwatch_event_rule.start_rds_weekday_mornings
# aws_cloudwatch_event_target.start_rds_target
# aws_cloudwatch_event_rule.stop_rds_weekday_evenings
# aws_cloudwatch_event_target.stop_rds_target
# aws_iam_role.rds_event_role
# aws_iam_policy.rds_event_policy
# aws_iam_role_policy_attachment.rds_event_policy_attach

# Add this temporary output to your main.tf file:
output "debug_postgres_outputs" {
  value = module.postgres
  description = "Debug postgres module outputs"
  sensitive = true
}

# Application S3 bucket for storing data
module "app_storage" {
  source = "../../modules/s3_bucket"

  bucket_name = "distillgenie"

  # Security settings - Enable all Block Public Access settings for SOC2 compliance
  versioning_enabled     = false  # Set to match current configuration
  block_public_acls      = true
  ignore_public_acls     = true
  block_public_policy    = true   # Changed to true to block public policies
  restrict_public_buckets = true  # Changed to true to restrict public buckets

  # Bucket policy for IAM access only - pre-signed URLs will still work
  bucket_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowDirectIAMAccess"
        Effect    = "Allow"
        Principal = {
          AWS = "arn:aws:iam::989284569019:user/distillgenie-s3-iam"
        }
        Action    = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource  = [
          "arn:aws:s3:::distillgenie",
          "arn:aws:s3:::distillgenie/*"
        ]
      },
      {
        Sid       = "AllowLambdaAccess"
        Effect    = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action    = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource  = [
          "arn:aws:s3:::distillgenie",
          "arn:aws:s3:::distillgenie/*"
        ]
      }
    ]
  })

  # Lifecycle rules
  lifecycle_rules = [
    {
      id      = "expire-old-results"
      status  = "Enabled"
      expiration = {
        days = 90  # Expire objects after 90 days
      }
    }
  ]

  tags = module.tags.tags
}

# Networking module for NAT Gateway and VPC endpoints
module "networking" {
  source = "../../modules/networking"

  app_name = var.app_name
  vpc_id   = "vpc-1daa4478"  # Default VPC where RDS is located

  # Use subnet-38c3e17e (us-east-1a) as the public subnet for NAT Gateway
  public_subnet_id = "subnet-38c3e17e"

  # Use the other subnets as private subnets
  private_subnet_ids = var.subnet_ids

  # Use the Lambda security group for VPC endpoints
  endpoint_security_group_ids = [aws_security_group.lambda.id]

  # Specify the Internet Gateway ID for the public subnet route table
  internet_gateway_id = "igw-6d99810f"  # Internet Gateway ID from the AWS CLI commands

  aws_region = var.aws_region

  # Create VPC endpoints for AWS services
  create_secretsmanager_endpoint = true
  create_logs_endpoint = true
  create_s3_endpoint = true
  create_dynamodb_endpoint = true

  tags = module.tags.tags
}

# Output the NAT Gateway details
output "nat_gateway_id" {
  description = "The ID of the NAT Gateway"
  value       = module.networking.nat_gateway_id
}

output "nat_gateway_public_ip" {
  description = "The public IP address of the NAT Gateway"
  value       = module.networking.nat_gateway_public_ip
}

output "public_route_table_id" {
  description = "The ID of the public subnet route table"
  value       = module.networking.public_route_table_id
}