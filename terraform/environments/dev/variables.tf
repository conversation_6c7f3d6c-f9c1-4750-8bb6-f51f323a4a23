variable "aws_region" {
  description = "The AWS region to deploy to"
  type        = string
  default     = "us-east-1"
}

variable "app_name" {
  description = "The name of the application"
  type        = string
  default     = "slackgenie"
}

variable "stage_name" {
  description = "The name of the API Gateway stage"
  type        = string
  default     = "dev"
}

variable "lambda_image_uri" {
  description = "The URI of the container image for the Lambda function (leave empty to use ECR repository)"
  type        = string
  default     = ""
}

variable "cors_origins" {
  description = "List of allowed origins for CORS"
  type        = list(string)
  default     = ["*"]
}

variable "use_vpc" {
  description = "Whether to deploy the Lambda function in a VPC"
  type        = bool
  default     = false
}

variable "subnet_ids" {
  description = "List of subnet IDs for VPC configuration"
  type        = list(string)
  default     = []
}

variable "security_group_ids" {
  description = "List of security group IDs for VPC configuration"
  type        = list(string)
  default     = []
}

variable "alarm_notification_arn" {
  description = "ARN of the SNS topic for CloudWatch alarms"
  type        = string
  default     = ""
}

# GitHub CI/CD variables
variable "github_repo_url" {
  description = "The URL of the GitHub repository"
  type        = string
  default     = ""
}

variable "github_webhook_enabled" {
  description = "Whether to enable GitHub webhook for CI/CD"
  type        = bool
  default     = false
}

variable "github_branch" {
  description = "The GitHub branch to trigger the CI/CD pipeline"
  type        = string
  default     = "main"
}

# Lambda environment secrets
variable "lambda_environment_secrets" {
  description = "Sensitive environment variables for Lambda function"
  type        = map(string)
  default     = {}
  sensitive   = true
}

# Tagging variables
variable "environment" {
  description = "Environment name (e.g. dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "project" {
  description = "Project name"
  type        = string
  default     = "SlackGenie"
}

variable "owner" {
  description = "Owner of the resources"
  type        = string
  default     = "DevOps"
}

variable "cost_center" {
  description = "Cost center for billing"
  type        = string
  default     = "Engineering"
}

variable "team" {
  description = "Team responsible for the resources"
  type        = string
  default     = "DevOps"
}

variable "additional_tags" {
  description = "Additional tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "db_password" {
  description = "Password for database"
  type        = string
  sensitive   = true
}

# AWS Budget variables
variable "aws_budget_amount" {
  description = "Monthly budget amount for the entire AWS account in USD"
  type        = number
  default     = 100
}

variable "budget_threshold_percent" {
  description = "Threshold percentage for budget notifications (e.g. 80 for 80%)"
  type        = number
  default     = 80
}

variable "ssm_parameter_name" {
  description = "SSM Parameter Store path for the Slack webhook URL"
  type        = string
  default     = "/custom/aws-cost-report/slack_webhook_url"
}

variable "enable_project_budgets" {
  description = "Whether to create separate budgets for each project"
  type        = bool
  default     = false
}

variable "project_budgets" {
  description = "Map of project names to budget amounts in USD"
  type        = map(number)
  default     = {}
}

variable "budget_email_subscribers" {
  description = "List of email addresses to notify for budget alerts"
  type        = list(string)
  default     = []
}