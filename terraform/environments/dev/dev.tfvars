# Region configuration
aws_region = "us-east-1"

# Application configuration
app_name    = "slackgenie"
stage_name  = "dev"

# CORS configuration
cors_origins = ["*"]

# VPC configuration (enabled to connect to RDS)
use_vpc = true
# Using subnets from the default VPC where R<PERSON> is located
# Note: subnet-38c3e17e is used as the public subnet for NAT Gateway and should not be in this list
subnet_ids = [
  "subnet-fdb9dc98",  # us-east-1b
  "subnet-26f6b40e"   # us-east-1c
]
# Security group will be created and referenced in main.tf

# Tagging configuration
environment = "dev"
project     = "SlackGenie"
owner       = "DevOps"
cost_center = "Engineering"
team        = "DevOps"

# CI/CD configuration
github_webhook_enabled = false
github_branch          = "develop"

# AWS Budget configuration
aws_budget_amount        = 100
budget_threshold_percent = 80
ssm_parameter_name       = "/custom/aws-cost-report/slack_webhook_url"
enable_project_budgets   = true

# Project-specific budgets - only enable if you have multiple projects with different tags
project_budgets = {
  "SlackGenie" = 100
}

# Email subscribers for budget alerts
budget_email_subscribers = [
  "<EMAIL>"  # Replace with your actual email address
]