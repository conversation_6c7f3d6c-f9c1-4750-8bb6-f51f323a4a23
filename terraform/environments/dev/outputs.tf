output "ecr_repository_url" {
  description = "The URL of the ECR repository"
  value       = module.ecr.repository_url
}

output "lambda_function_name" {
  description = "The name of the Lambda function"
  value       = module.lambda.lambda_function_name
}

output "lambda_function_arn" {
  description = "The ARN of the Lambda function"
  value       = module.lambda.lambda_function_arn
}

output "api_gateway_endpoint" {
  description = "The endpoint URL of the API Gateway"
  value       = module.api_gateway.api_gateway_endpoint
}

output "push_commands" {
  description = "Commands to build and push the Docker image to ECR"
  value       = <<EOF
# Login to ECR
aws ecr get-login-password --region ${var.aws_region} | docker login --username AWS --password-stdin ${module.ecr.repository_url}

# Build the Docker image
docker build -t ${module.ecr.repository_url}:latest .

# Push the Docker image to ECR
docker push ${module.ecr.repository_url}:latest
EOF
}

output "budget_lambda_function_name" {
  description = "The name of the budget notification Lambda function"
  value       = module.budget_notifications.lambda_function_name
}

output "budget_test_command" {
  description = "Command to test the budget notification Lambda function"
  value       = module.budget_notifications.test_command
}

output "account_budget_id" {
  description = "The ID of the account budget"
  value       = module.budget_notifications.account_budget_id
}

output "project_budget_ids" {
  description = "Map of project names to budget IDs"
  value       = module.budget_notifications.project_budget_ids
}