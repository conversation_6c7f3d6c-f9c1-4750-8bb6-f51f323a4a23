# Get current account information
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

resource "aws_ecr_repository" "app" {
  name                 = var.repository_name
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }

  encryption_configuration {
    encryption_type = "KMS"
    kms_key         = var.kms_key_arn != null ? var.kms_key_arn : null
  }

  tags = var.tags
}

resource "aws_ecr_lifecycle_policy" "app" {
  repository = aws_ecr_repository.app.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1
        description  = "Keep only the last ${var.image_count_limit} images"
        selection = {
          tagStatus     = "any"
          countType     = "imageCountMoreThan"
          countNumber   = var.image_count_limit
        }
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# Repository policy that restricts who can push/pull images
locals {
  default_principals = [
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/terraform-deployment-role",
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/axel.von<PERSON><PERSON><PERSON>@distillery.com",
    "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/<EMAIL>",
    "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/terraform-deployment-role/terraform-session"
  ]

  # Sort all principals to ensure consistent ordering
  all_principals = sort(concat(var.allowed_principals, local.default_principals))
}

resource "aws_ecr_repository_policy" "app" {
  repository = aws_ecr_repository.app.name

  # Ignore changes to policy to prevent constant reordering of principals
  lifecycle {
    ignore_changes = [policy]
  }

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "AllowPushPull"
        Effect    = "Allow"
        Principal = {
          AWS = local.all_principals
        }
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload"
        ]
      },
      {
        Sid       = "LambdaECRImageRetrievalPolicy"
        Effect    = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = [
          "ecr:BatchGetImage",
          "ecr:GetDownloadUrlForLayer",
          "ecr:SetRepositoryPolicy",
          "ecr:DeleteRepositoryPolicy",
          "ecr:GetRepositoryPolicy",
        ]
        Condition = {
          StringLike = {
            "aws:sourceArn" = "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:*"
          }
        }
      }
    ]
  })
}