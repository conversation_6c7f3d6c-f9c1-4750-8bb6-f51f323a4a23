variable "repository_name" {
  description = "The name of the ECR repository"
  type        = string
}

variable "kms_key_arn" {
  description = "The ARN of the KMS key to use for encryption"
  type        = string
  default     = null
}

variable "image_count_limit" {
  description = "The maximum number of images to keep in the repository"
  type        = number
  default     = 10
}

variable "allowed_principals" {
  description = "List of AWS principal ARNs allowed to push/pull images"
  type        = list(string)
  default     = []
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
} 