variable "resource_name" {
  description = "Nombre del recurso a monitorear (ej: nombre de la tabla DynamoDB)"
  type        = string
}

variable "alarm_prefix" {
  description = "Prefijo para los nombres de las alarmas"
  type        = string
  default     = "slackgenie"
}

variable "read_capacity_threshold" {
  description = "Umbral para la alarma de capacidad de lectura"
  type        = number
  default     = 80
}

variable "write_capacity_threshold" {
  description = "Umbral para la alarma de capacidad de escritura"
  type        = number
  default     = 80
}

variable "tags" {
  description = "Tags a aplicar a los recursos"
  type        = map(string)
  default     = {}
} 