resource "aws_cloudwatch_metric_alarm" "dynamodb_read_capacity" {
  alarm_name          = "${var.alarm_prefix}-${var.resource_name}-read-capacity"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "ConsumedReadCapacityUnits"
  namespace           = "AWS/DynamoDB"
  period              = 300
  statistic           = "Maximum"
  threshold           = var.read_capacity_threshold
  alarm_description   = "Monitorea la capacidad de lectura consumida por la tabla DynamoDB ${var.resource_name}"
  treat_missing_data  = "notBreaching"

  dimensions = {
    TableName = var.resource_name
  }
  
  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "dynamodb_write_capacity" {
  alarm_name          = "${var.alarm_prefix}-${var.resource_name}-write-capacity"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "ConsumedWriteCapacityUnits"
  namespace           = "AWS/DynamoDB"
  period              = 300
  statistic           = "Maximum"
  threshold           = var.write_capacity_threshold
  alarm_description   = "Monitorea la capacidad de escritura consumida por la tabla DynamoDB ${var.resource_name}"
  treat_missing_data  = "notBreaching"

  dimensions = {
    TableName = var.resource_name
  }
  
  tags = var.tags
} 