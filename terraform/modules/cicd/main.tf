resource "aws_iam_role" "cicd_role" {
  name = "${var.app_name}-cicd-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Principal = {
        Service = "codebuild.amazonaws.com"
      }
    }]
  })
  
  tags = var.tags
}

# Allow CI/CD to access ECR
resource "aws_iam_policy" "cicd_ecr_policy" {
  name        = "${var.app_name}-cicd-ecr-policy"
  description = "Allow CI/CD to push to ECR"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:CompleteLayerUpload",
          "ecr:GetAuthorizationToken",
          "ecr:InitiateLayerUpload",
          "ecr:PutImage",
          "ecr:UploadLayerPart",
          "ecr:BatchGetImage",
          "ecr:GetDownloadUrlForLayer"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
  
  tags = var.tags
}

# Allow CI/CD to update Lambda function
resource "aws_iam_policy" "cicd_lambda_policy" {
  name        = "${var.app_name}-cicd-lambda-policy"
  description = "Allow CI/CD to update Lambda function"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "lambda:UpdateFunctionCode",
          "lambda:GetFunction"
        ]
        Effect   = "Allow"
        Resource = var.lambda_function_arn
      }
    ]
  })
  
  tags = var.tags
}

# Allow CI/CD to use CloudWatch logs
resource "aws_iam_policy" "cicd_logs_policy" {
  name        = "${var.app_name}-cicd-logs-policy"
  description = "Allow CI/CD to use CloudWatch logs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
  
  tags = var.tags
}

# Attach policies to role
resource "aws_iam_role_policy_attachment" "cicd_ecr_attachment" {
  role       = aws_iam_role.cicd_role.name
  policy_arn = aws_iam_policy.cicd_ecr_policy.arn
}

resource "aws_iam_role_policy_attachment" "cicd_lambda_attachment" {
  role       = aws_iam_role.cicd_role.name
  policy_arn = aws_iam_policy.cicd_lambda_policy.arn
}

resource "aws_iam_role_policy_attachment" "cicd_logs_attachment" {
  role       = aws_iam_role.cicd_role.name
  policy_arn = aws_iam_policy.cicd_logs_policy.arn
}

# CodeBuild project for CI/CD
resource "aws_codebuild_project" "app_build" {
  name          = "${var.app_name}-build"
  description   = "CI/CD pipeline for ${var.app_name}"
  build_timeout = "15"
  service_role  = aws_iam_role.cicd_role.arn

  artifacts {
    type = "NO_ARTIFACTS"
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:4.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "ECR_REPOSITORY_URI"
      value = var.ecr_repository_url
    }

    environment_variable {
      name  = "LAMBDA_FUNCTION_NAME"
      value = var.lambda_function_name
    }
  }

  source {
    type            = "GITHUB"
    location        = var.github_repo_url
    git_clone_depth = 1
    buildspec       = <<EOF
version: 0.2

phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      - curl -L https://dl.dagger.io/dagger/install.sh | DAGGER_VERSION=0.9.3 sh
      - mv bin/dagger /usr/local/bin
      - dagger version

  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ECR_REPOSITORY_URI

  build:
    commands:
      - echo Running linting...
      - dagger call lint
      - echo Running tests...
      - dagger call test
      - echo Building the Docker image...
      - docker build -t $ECR_REPOSITORY_URI:latest -f Dockerfile.lambda .
      - docker tag $ECR_REPOSITORY_URI:latest $ECR_REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION

  post_build:
    commands:
      - echo Pushing the Docker image...
      - docker push $ECR_REPOSITORY_URI:latest
      - docker push $ECR_REPOSITORY_URI:$CODEBUILD_RESOLVED_SOURCE_VERSION
      - echo Updating Lambda function...
      - aws lambda update-function-code --function-name $LAMBDA_FUNCTION_NAME --image-uri $ECR_REPOSITORY_URI:latest

EOF
  }

  logs_config {
    cloudwatch_logs {
      group_name  = "/aws/codebuild/${var.app_name}-build"
      stream_name = "build-log"
    }
  }
  
  tags = var.tags
}

# GitHub webhook for CI/CD (if specified)
resource "aws_codebuild_webhook" "github_webhook" {
  count        = var.github_webhook_enabled ? 1 : 0
  project_name = aws_codebuild_project.app_build.name
  
  filter_group {
    filter {
      type    = "EVENT"
      pattern = "PUSH"
    }

    filter {
      type    = "HEAD_REF"
      pattern = var.github_branch
    }
  }
} 