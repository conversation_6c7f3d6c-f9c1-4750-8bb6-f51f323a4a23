variable "app_name" {
  description = "The name of the application"
  type        = string
}

variable "ecr_repository_url" {
  description = "The URL of the ECR repository"
  type        = string
}

variable "lambda_function_name" {
  description = "The name of the Lambda function"
  type        = string
}

variable "lambda_function_arn" {
  description = "The ARN of the Lambda function"
  type        = string
}

variable "github_repo_url" {
  description = "The URL of the GitHub repository"
  type        = string
}

variable "github_webhook_enabled" {
  description = "Whether to enable GitHub webhook for CI/CD"
  type        = bool
  default     = false
}

variable "github_branch" {
  description = "The GitHub branch to trigger the CI/CD pipeline"
  type        = string
  default     = "main"
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
} 