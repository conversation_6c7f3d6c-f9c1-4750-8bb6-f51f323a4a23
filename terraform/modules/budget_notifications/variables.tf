variable "aws_region" {
  description = "The AWS region to deploy to"
  type        = string
  default     = "us-east-1"
}

variable "project_name" {
  description = "Name of the project, used as a prefix for resource names"
  type        = string
}

variable "environment" {
  description = "Environment name (e.g. dev, staging, prod)"
  type        = string
  default     = "dev"
}

variable "account_budget_amount" {
  description = "Monthly budget amount for the entire AWS account in USD"
  type        = number
}

variable "threshold_percent" {
  description = "Threshold percentage for budget notifications (e.g. 80 for 80%)"
  type        = number
  default     = 80
}

variable "forecast_threshold_percent" {
  description = "Threshold percentage for forecasted budget notifications (e.g. 100 for 100%)"
  type        = number
  default     = 100
}

variable "enable_forecasted_notifications" {
  description = "Whether to enable forecasted budget notifications"
  type        = bool
  default     = true
}

variable "enable_project_budgets" {
  description = "Whether to create separate budgets for each project"
  type        = bool
  default     = false
}

variable "project_budgets" {
  description = "Map of project names to budget amounts in USD"
  type        = map(number)
  default     = {}
}

variable "email_subscribers" {
  description = "List of email addresses to notify for budget alerts"
  type        = list(string)
  default     = []
}

variable "ssm_parameter_name" {
  description = "SSM Parameter Store path for the Slack webhook URL"
  type        = string
  default     = "/custom/aws-cost-report/slack_webhook_url"
}

variable "log_retention_days" {
  description = "Number of days to retain Lambda logs"
  type        = number
  default     = 365
}

variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}
