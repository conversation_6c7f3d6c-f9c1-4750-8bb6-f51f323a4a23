provider "aws" {
  region = var.aws_region
}

# Create a budget for the entire AWS account
resource "aws_budgets_budget" "account_budget" {
  name              = "${var.project_name}-account-budget"
  budget_type       = "COST"
  time_unit         = "MONTHLY"
  time_period_start = "2023-01-01_00:00"

  # Set the budget limit
  limit_amount = var.account_budget_amount
  limit_unit   = "USD"

  # Configure notifications - email only, no SNS
  notification {
    comparison_operator        = "GREATER_THAN"
    threshold                  = var.threshold_percent
    threshold_type             = "PERCENTAGE"
    notification_type          = "ACTUAL"
    subscriber_email_addresses = var.email_subscribers
  }

  # Add forecasted notification if enabled
  dynamic "notification" {
    for_each = var.enable_forecasted_notifications ? [1] : []
    content {
      comparison_operator        = "GREATER_THAN"
      threshold                  = var.forecast_threshold_percent
      threshold_type             = "PERCENTAGE"
      notification_type          = "FORECASTED"
      subscriber_email_addresses = var.email_subscribers
    }
  }

  # Add cost filters for the project tag if specified
  cost_filter {
    name = "TagKeyValue"
    values = ["Project$SlackGenie"]
  }
}

# Create budgets for each tagged project if enabled
resource "aws_budgets_budget" "project_budgets" {
  for_each = var.enable_project_budgets ? var.project_budgets : {}

  name              = "${var.project_name}-${each.key}-budget"
  budget_type       = "COST"
  time_unit         = "MONTHLY"
  time_period_start = "2023-01-01_00:00"

  # Set the budget limit
  limit_amount = each.value
  limit_unit   = "USD"

  # Configure notifications - email only, no SNS
  notification {
    comparison_operator        = "GREATER_THAN"
    threshold                  = var.threshold_percent
    threshold_type             = "PERCENTAGE"
    notification_type          = "ACTUAL"
    subscriber_email_addresses = var.email_subscribers
  }

  # Add forecasted notification if enabled
  dynamic "notification" {
    for_each = var.enable_forecasted_notifications ? [1] : []
    content {
      comparison_operator        = "GREATER_THAN"
      threshold                  = var.forecast_threshold_percent
      threshold_type             = "PERCENTAGE"
      notification_type          = "FORECASTED"
      subscriber_email_addresses = var.email_subscribers
    }
  }

  # Add cost filters for the specific project tag
  cost_filter {
    name = "TagKeyValue"
    values = ["Project$${each.key}"]
  }
}

# Lambda function to send budget notifications to Slack
data "archive_file" "lambda_zip" {
  type        = "zip"
  output_path = "${path.module}/budget_notification_lambda.zip"

  source {
    content  = <<EOF
import json
import logging
import os
import urllib.request
import boto3

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize SSM client
ssm = boto3.client('ssm')

def get_slack_webhook_url():
    """
    Get the Slack webhook URL from SSM Parameter Store
    """
    try:
        # Get the parameter name from environment variable
        param_name = os.environ['SLACK_WEBHOOK_URL_PARAM']

        # Get the parameter from SSM
        response = ssm.get_parameter(
            Name=param_name,
            WithDecryption=True
        )

        return response['Parameter']['Value']
    except Exception as e:
        logger.error(f"Error retrieving Slack webhook URL from SSM: {str(e)}")
        raise

def lambda_handler(event, context):
    """
    Process AWS Budget notifications and send to Slack
    """
    logger.info(f"Processing budget notification. Event: {json.dumps(event)}")

    try:
        # Get AWS account ID
        sts_client = boto3.client('sts')
        account_id = sts_client.get_caller_identity()['Account']

        # For direct invocation from AWS Budgets
        if 'source' in event and event['source'] == 'aws.budgets':
            # Extract budget details from the event
            budget_name = event.get('detail', {}).get('budgetName', 'Unknown Budget')
            budget_type = "COST"  # Default to COST type

            # Determine if this is an actual or forecasted notification
            notification_type = "ACTUAL"  # Default to ACTUAL
            if 'Forecasted' in event.get('detail', {}).get('notificationType', ''):
                notification_type = "FORECASTED"

            # Get threshold and actual/forecasted spend
            threshold = event.get('detail', {}).get('threshold', '0')
            actual_spend = event.get('detail', {}).get('actualSpend', '0')
            forecasted_spend = event.get('detail', {}).get('forecastedSpend', '0')
            budget_limit = event.get('detail', {}).get('budgetLimit', '0')

            # Create a message object
            message = {
                'budgetName': budget_name,
                'accountId': account_id,
                'budgetType': budget_type,
                'notificationType': notification_type,
                'threshold': threshold,
                'actualSpend': actual_spend,
                'forecastedSpend': forecasted_spend,
                'budgetLimit': budget_limit
            }
        elif isinstance(event, dict) and 'Records' in event:
            # This is likely an SNS notification
            logger.info("Processing SNS notification")
            try:
                # Try to parse the SNS message
                sns_message = json.loads(event['Records'][0]['Sns']['Message'])
                logger.info(f"SNS Message: {json.dumps(sns_message)}")

                # Extract budget details
                budget_name = sns_message.get('budgetName', 'Unknown Budget')
                budget_type = sns_message.get('budgetType', 'COST')
                notification_type = sns_message.get('notificationType', 'ACTUAL')
                threshold = sns_message.get('threshold', '0')
                actual_spend = sns_message.get('actualSpend', '0')
                forecasted_spend = sns_message.get('forecastedSpend', '0')
                budget_limit = sns_message.get('budgetLimit', '0')

                # Create a message object
                message = {
                    'budgetName': budget_name,
                    'accountId': account_id,
                    'budgetType': budget_type,
                    'notificationType': notification_type,
                    'threshold': threshold,
                    'actualSpend': actual_spend,
                    'forecastedSpend': forecasted_spend,
                    'budgetLimit': budget_limit
                }
            except Exception as e:
                logger.error(f"Error parsing SNS message: {str(e)}")
                message = event
        else:
            # For testing or other invocation methods
            logger.info("Processing direct invocation or test event")
            message = event

            # If this is a test event, fetch real cost data
            if 'detail-type' not in event and 'Records' not in event:
                try:
                    # Get budget information from AWS Budgets API
                    budgets_client = boto3.client('budgets')
                    budgets = budgets_client.describe_budgets(
                        AccountId=account_id
                    )

                    # Initialize variables
                    budget_name = 'SlackGenie-account-budget'
                    budget_limit = '100'
                    budget_type = 'COST'

                    # Get budget details if available
                    if budgets and 'Budgets' in budgets and len(budgets['Budgets']) > 0:
                        budget = budgets['Budgets'][0]
                        budget_name = budget.get('BudgetName', 'SlackGenie-account-budget')
                        budget_limit = budget.get('BudgetLimit', {}).get('Amount', '100')
                        budget_type = budget.get('BudgetType', 'COST')

                    # Get current date information
                    import datetime
                    now = datetime.datetime.now()
                    first_day_of_month = datetime.datetime(now.year, now.month, 1).strftime('%Y-%m-%d')
                    today = now.strftime('%Y-%m-%d')
                    last_day_of_month = datetime.datetime(now.year, now.month + 1, 1) - datetime.timedelta(days=1)
                    last_day_of_month = last_day_of_month.strftime('%Y-%m-%d')

                    # Get actual cost data from Cost Explorer
                    ce_client = boto3.client('ce')

                    # Get month-to-date cost
                    cost_response = ce_client.get_cost_and_usage(
                        TimePeriod={
                            'Start': first_day_of_month,
                            'End': today
                        },
                        Granularity='MONTHLY',
                        Metrics=['UnblendedCost'],
                        GroupBy=[
                            {
                                'Type': 'TAG',
                                'Key': 'Project'
                            }
                        ]
                    )

                    # Extract cost data
                    actual_spend = '0'
                    if cost_response and 'ResultsByTime' in cost_response and len(cost_response['ResultsByTime']) > 0:
                        for group in cost_response['ResultsByTime'][0].get('Groups', []):
                            if group.get('Keys', [''])[0] == 'Project$SlackGenie':
                                actual_spend = group.get('Metrics', {}).get('UnblendedCost', {}).get('Amount', '0')
                                break

                    # Try to get forecast for the month
                    forecasted_spend = '0'
                    total_forecast = actual_spend  # Default to actual spend if forecast fails

                    try:
                        forecast_response = ce_client.get_cost_forecast(
                            TimePeriod={
                                'Start': today,
                                'End': last_day_of_month
                            },
                            Granularity='MONTHLY',
                            Metric='UNBLENDED_COST',
                            Filter={
                                'Tags': {
                                    'Key': 'Project',
                                    'Values': ['SlackGenie']
                                }
                            }
                        )

                        # Extract forecast data
                        if forecast_response and 'ForecastResultsByTime' in forecast_response and len(forecast_response['ForecastResultsByTime']) > 0:
                            forecasted_spend = forecast_response['ForecastResultsByTime'][0].get('MeanValue', '0')

                            # Calculate total forecast (actual + forecasted)
                            total_forecast = str(float(actual_spend) + float(forecasted_spend))
                    except Exception as e:
                        logger.warning(f"Could not get forecast data: {str(e)}")
                        # If we can't get a forecast, estimate based on daily average
                        try:
                            # Calculate days in month and days passed
                            days_in_month = (datetime.datetime(now.year, now.month + 1, 1) - datetime.datetime(now.year, now.month, 1)).days
                            days_passed = now.day

                            if days_passed > 0:
                                # Calculate daily average and project for the month
                                daily_average = float(actual_spend) / days_passed
                                total_forecast = str(daily_average * days_in_month)
                            else:
                                total_forecast = actual_spend
                        except Exception as inner_e:
                            logger.warning(f"Could not estimate forecast: {str(inner_e)}")
                            total_forecast = actual_spend

                    # Get previous month's cost for comparison
                    last_month = now.replace(day=1) - datetime.timedelta(days=1)
                    first_day_last_month = datetime.datetime(last_month.year, last_month.month, 1).strftime('%Y-%m-%d')
                    last_day_last_month = last_month.strftime('%Y-%m-%d')

                    previous_month_response = ce_client.get_cost_and_usage(
                        TimePeriod={
                            'Start': first_day_last_month,
                            'End': last_day_last_month
                        },
                        Granularity='MONTHLY',
                        Metrics=['UnblendedCost'],
                        GroupBy=[
                            {
                                'Type': 'TAG',
                                'Key': 'Project'
                            }
                        ]
                    )

                    # Extract previous month cost
                    previous_month_spend = '0'
                    if previous_month_response and 'ResultsByTime' in previous_month_response and len(previous_month_response['ResultsByTime']) > 0:
                        for group in previous_month_response['ResultsByTime'][0].get('Groups', []):
                            if group.get('Keys', [''])[0] == 'Project$SlackGenie':
                                previous_month_spend = group.get('Metrics', {}).get('UnblendedCost', {}).get('Amount', '0')
                                break

                    # Calculate variance
                    variance = '0'
                    variance_percent = '0'
                    if float(previous_month_spend) > 0:
                        variance = str(float(actual_spend) - float(previous_month_spend))
                        variance_percent = str(round((float(actual_spend) - float(previous_month_spend)) / float(previous_month_spend) * 100, 1))

                    # Create a message with real cost data
                    message = {
                        'budgetName': budget_name,
                        'accountId': account_id,
                        'budgetType': budget_type,
                        'notificationType': 'ACTUAL',
                        'threshold': '80',
                        'actualSpend': actual_spend,
                        'forecastedSpend': total_forecast,
                        'budgetLimit': budget_limit,
                        'month': now.strftime('%B %Y'),
                        'previousMonthSpend': previous_month_spend,
                        'variance': variance,
                        'variancePercent': variance_percent,
                        'isTest': True
                    }

                    logger.info(f"Created real cost data notification: {json.dumps(message)}")
                except Exception as e:
                    logger.error(f"Error getting cost information: {str(e)}")
                    # Fallback to a sample message if there's an error
                    message = {
                        'budgetName': 'SlackGenie-account-budget',
                        'accountId': account_id,
                        'budgetType': 'COST',
                        'notificationType': 'ACTUAL',
                        'threshold': '80',
                        'actualSpend': '1.84',
                        'forecastedSpend': '11.06',
                        'budgetLimit': '100',
                        'month': 'Current Month',
                        'previousMonthSpend': '1.41',
                        'variance': '0.43',
                        'variancePercent': '30.3',
                        'isTest': True
                    }

        # Get the Slack webhook URL from SSM Parameter Store
        webhook_url = get_slack_webhook_url()

        # Format the Slack message
        slack_message = format_slack_message(message)

        # Send the message to Slack
        send_slack_notification(webhook_url, slack_message)

        return {
            'statusCode': 200,
            'body': json.dumps('Successfully sent budget notification to Slack')
        }
    except Exception as e:
        logger.error(f"Error processing budget notification: {str(e)}")
        raise

def format_slack_message(message):
    """
    Format the message for Slack
    """
    # Extract budget details
    budget_name = message.get('budgetName', 'Unknown Budget')
    account_id = message.get('accountId', 'Unknown Account')
    budget_type = message.get('budgetType', 'Unknown Type')

    # Determine if this is an actual or forecasted notification
    notification_type = message.get('notificationType', 'ACTUAL')
    is_forecast = notification_type == 'FORECASTED'
    is_test = message.get('isTest', False)

    # Get threshold and actual/forecasted spend
    threshold = message.get('threshold', '0')
    actual_spend = message.get('actualSpend', '0')
    forecasted_spend = message.get('forecastedSpend', '0')
    budget_limit = message.get('budgetLimit', '0')

    # Format numbers for display
    try:
        actual_spend_float = float(actual_spend)
        forecasted_spend_float = float(forecasted_spend)
        budget_limit_float = float(budget_limit)

        actual_spend_formatted = "$" + "{:,.2f}".format(actual_spend_float)
        forecasted_spend_formatted = "$" + "{:,.2f}".format(forecasted_spend_float)
        budget_limit_formatted = "$" + "{:,.2f}".format(budget_limit_float)

        # Calculate percentage of budget used
        if budget_limit_float > 0:
            percent_used = (actual_spend_float / budget_limit_float) * 100
            percent_forecast = (forecasted_spend_float / budget_limit_float) * 100
        else:
            percent_used = 0
            percent_forecast = 0

        percent_used_formatted = "{:.1f}%".format(percent_used)
        percent_forecast_formatted = "{:.1f}%".format(percent_forecast)
    except ValueError:
        actual_spend_formatted = "$" + actual_spend
        forecasted_spend_formatted = "$" + forecasted_spend
        budget_limit_formatted = "$" + budget_limit
        percent_used_formatted = "0%"
        percent_forecast_formatted = "0%"

    # Get additional information for test messages
    month = message.get('month', 'Current Month')
    previous_month_spend = message.get('previousMonthSpend', '0')
    variance = message.get('variance', '0')
    variance_percent = message.get('variancePercent', '0')

    try:
        previous_month_float = float(previous_month_spend)
        variance_float = float(variance)

        previous_month_formatted = "$" + "{:,.2f}".format(previous_month_float)
        variance_formatted = "$" + "{:,.2f}".format(variance_float)
        variance_percent_formatted = variance_percent + "%"
    except ValueError:
        previous_month_formatted = "$" + previous_month_spend
        variance_formatted = "$" + variance
        variance_percent_formatted = variance_percent + "%"

    # Determine status indicator and color
    if is_test:
        title = ":bar_chart: *AWS Cost Report: " + month + "*"
        color = "#2eb886"  # Green for test/reports
    elif is_forecast:
        title = ":warning: *Forecasted Budget Alert: " + budget_name + "*"
        color = "#FFA500"  # Orange for forecasts
    else:
        title = ":rotating_light: *Budget Alert: " + budget_name + "*"
        color = "#FF0000"  # Red for actual alerts

    # Create the Slack message
    blocks = [
        {
            "type": "header",
            "text": {
                "type": "plain_text",
                "text": title.replace('*', '')
            }
        },
        {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": "*Account ID:*\n" + account_id
                },
                {
                    "type": "mrkdwn",
                    "text": "*Budget Type:*\n" + budget_type
                }
            ]
        },
        {
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": "*Budget Limit:*\n" + budget_limit_formatted
                },
                {
                    "type": "mrkdwn",
                    "text": "*Current Spend:*\n" + actual_spend_formatted + " (" + percent_used_formatted + ")"
                }
            ]
        }
    ]

    # Add forecast information
    blocks.append({
        "type": "section",
        "fields": [
            {
                "type": "mrkdwn",
                "text": "*Forecasted Total:*\n" + forecasted_spend_formatted + " (" + percent_forecast_formatted + ")"
            },
            {
                "type": "mrkdwn",
                "text": "*Threshold:*\n" + threshold + "%"
            }
        ]
    })

    # Add comparison with previous month for test/report messages
    if is_test and float(previous_month_spend) > 0:
        # Determine if variance is positive or negative
        variance_indicator = ":chart_with_upwards_trend:" if float(variance) > 0 else ":chart_with_downwards_trend:"

        blocks.append({
            "type": "section",
            "fields": [
                {
                    "type": "mrkdwn",
                    "text": "*Previous Month:*\n" + previous_month_formatted
                },
                {
                    "type": "mrkdwn",
                    "text": "*Variance:*\n" + variance_indicator + " " + variance_formatted + " (" + variance_percent_formatted + ")"
                }
            ]
        })

    # Add a divider
    blocks.append({
        "type": "divider"
    })

    # Add a footer with link to AWS Cost Explorer
    blocks.append({
        "type": "context",
        "elements": [
            {
                "type": "mrkdwn",
                "text": "<https://console.aws.amazon.com/cost-management/home?region=us-east-1#/cost-explorer|View in AWS Cost Explorer> • <https://console.aws.amazon.com/billing/home?region=us-east-1#/budgets|Manage Budgets>"
            }
        ]
    })

    slack_message = {
        "attachments": [
            {
                "color": color,
                "blocks": blocks
            }
        ]
    }

    return slack_message

def send_slack_notification(webhook_url, slack_message):
    """
    Send notification to Slack
    """
    try:
        data = json.dumps(slack_message).encode('utf-8')
        req = urllib.request.Request(
            webhook_url,
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        response = urllib.request.urlopen(req)
        logger.info(f"Slack notification sent. Response: {response.read().decode('utf-8')}")
    except Exception as e:
        logger.error(f"Error sending Slack notification: {str(e)}")
        raise
EOF
    filename = "lambda_function.py"
  }
}

# IAM role for the Lambda function
resource "aws_iam_role" "lambda_role" {
  name = "${var.project_name}-budget-notification-lambda-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = local.tags
}

# IAM policy for the Lambda function
resource "aws_iam_policy" "lambda_policy" {
  name        = "${var.project_name}-budget-notification-lambda-policy"
  description = "Policy for budget notification Lambda function"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter"
        ]
        Resource = "arn:aws:ssm:${var.aws_region}:*:parameter${var.ssm_parameter_name}"
      },
      {
        Effect = "Allow"
        Action = [
          "sts:GetCallerIdentity"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "budgets:DescribeBudgets",
          "budgets:ViewBudget"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ce:GetCostAndUsage",
          "ce:GetCostForecast"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "lambda_policy_attachment" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.lambda_policy.arn
}

# Lambda function for budget notifications
resource "aws_lambda_function" "budget_notification" {
  function_name    = "${var.project_name}-budget-notification"
  role             = aws_iam_role.lambda_role.arn
  handler          = "lambda_function.lambda_handler"
  runtime          = "python3.9"
  timeout          = 30
  memory_size      = 128

  filename         = data.archive_file.lambda_zip.output_path
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256

  environment {
    variables = {
      SLACK_WEBHOOK_URL_PARAM = var.ssm_parameter_name
    }
  }

  tags = local.tags
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "lambda_logs" {
  name              = "/aws/lambda/${aws_lambda_function.budget_notification.function_name}"
  retention_in_days = 365

  tags = local.tags
}

# CloudWatch Alarm for Lambda Errors
resource "aws_cloudwatch_metric_alarm" "lambda_error_alarm" {
  alarm_name          = "${var.project_name}-budget-notification-lambda-errors"
  alarm_description   = "Alarm when the budget notification Lambda function experiences errors"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = 300 # 5 minutes
  statistic           = "Sum"
  threshold           = 1 # Trigger if there's 1 or more errors in the period

  dimensions = {
    FunctionName = aws_lambda_function.budget_notification.function_name
  }

  # No alarm_actions specified to avoid using SNS (as requested)

  tags = local.tags
}

# Local variables
locals {
  tags = merge(
    var.tags,
    {
      Project     = var.project_name
      Environment = var.environment
      ManagedBy   = "terraform"
    }
  )
}
