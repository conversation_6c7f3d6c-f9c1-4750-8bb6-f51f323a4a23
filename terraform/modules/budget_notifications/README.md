# AWS Budget Notifications

This Terraform module deploys a solution that sends AWS budget notifications via email. The solution uses AWS Budgets to monitor your AWS spending and send alerts when thresholds are exceeded.

## Features

- Account-wide budget monitoring
- Project-specific budget monitoring using tags
- Actual and forecasted budget notifications
- Email notifications with budget alerts
- Configurable budget thresholds

## Tag Requirements

This module uses AWS cost allocation tags to track spending by project. For this to work:

1. Make sure your resources are tagged with a "Project" tag (e.g., "Project" = "DistillGenie")
2. Activate "Project" as a cost allocation tag in the AWS Billing console
3. Note that it can take up to 24 hours for new cost allocation tags to be available for budgets

## Usage

```hcl
module "budget_notifications" {
  source = "../../modules/budget_notifications"

  project_name         = "MyProject"
  environment          = "dev"
  account_budget_amount = 100
  threshold_percent    = 80
  email_subscribers    = ["<EMAIL>"]

  # Enable project-specific budgets (optional)
  enable_project_budgets = true
  project_budgets        = {
    "DistillGenie" = 100  # Budget for resources tagged with Project=DistillGenie
  }

  # Email subscribers already set above

  # Additional tags
  tags = {
    Owner       = "DevOps"
    CostCenter  = "Engineering"
  }
}
```

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|----------|
| aws_region | The AWS region to deploy to | string | "us-east-1" | no |
| project_name | Name of the project, used as a prefix for resource names | string | - | yes |
| environment | Environment name (e.g. dev, staging, prod) | string | "dev" | no |
| account_budget_amount | Monthly budget amount for the entire AWS account in USD | number | - | yes |
| threshold_percent | Threshold percentage for budget notifications | number | 80 | no |
| forecast_threshold_percent | Threshold percentage for forecasted budget notifications | number | 100 | no |
| enable_forecasted_notifications | Whether to enable forecasted budget notifications | bool | true | no |
| enable_project_budgets | Whether to create separate budgets for each project | bool | false | no |
| project_budgets | Map of project names to budget amounts in USD | map(number) | {} | no |
| email_subscribers | List of email addresses to notify for budget alerts | list(string) | [] | no |

| tags | Additional tags to apply to all resources | map(string) | {} | no |

## Outputs

| Name | Description |
|------|-------------|
| account_budget_id | ID of the account budget |
| project_budget_ids | Map of project names to budget IDs |

## Email Notifications

The module sends email notifications when budget thresholds are exceeded. The notifications include:

- Budget name
- Account ID
- Budget type
- Budget limit
- Actual or forecasted spend
- Threshold percentage

## Prerequisites

1. AWS CLI configured with appropriate credentials
2. Terraform installed
3. Valid email addresses for notifications

## Cost Considerations

The cost of running this solution is minimal:

1. **AWS Budgets**: Free for the first two budgets, then $0.02 per budget per day
2. **Email Notifications**: Free

