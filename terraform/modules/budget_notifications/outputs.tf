output "account_budget_id" {
  description = "ID of the account budget"
  value       = aws_budgets_budget.account_budget.id
}

output "project_budget_ids" {
  description = "Map of project names to budget IDs"
  value       = { for k, v in aws_budgets_budget.project_budgets : k => v.id }
}

output "lambda_function_name" {
  description = "Name of the Lambda function"
  value       = aws_lambda_function.budget_notification.function_name
}

output "lambda_function_arn" {
  description = "ARN of the Lambda function"
  value       = aws_lambda_function.budget_notification.arn
}

output "cloudwatch_alarm_name" {
  description = "Name of the CloudWatch alarm for Lambda errors"
  value       = aws_cloudwatch_metric_alarm.lambda_error_alarm.alarm_name
}

output "test_command" {
  description = "Command to test the Lambda function"
  value       = "aws lambda invoke --function-name ${aws_lambda_function.budget_notification.function_name} --payload '{}' response.json && cat response.json"
}
