output "api_gateway_id" {
  description = "The ID of the API Gateway"
  value       = aws_apigatewayv2_api.fastapi_gateway.id
}

output "api_gateway_execution_arn" {
  description = "The execution ARN of the API Gateway"
  value       = aws_apigatewayv2_api.fastapi_gateway.execution_arn
}

output "api_gateway_endpoint" {
  description = "The endpoint URL of the API Gateway"
  value       = aws_apigatewayv2_stage.default.invoke_url
}

output "stage_name" {
  description = "The name of the deployed stage"
  value       = aws_apigatewayv2_stage.default.name
}

output "waf_web_acl_arn" {
  description = "The ARN of the WAF Web ACL (if enabled)"
  value       = var.enable_waf ? aws_wafv2_web_acl.api_gateway_waf[0].arn : null
} 