output "nat_gateway_id" {
  description = "The ID of the NAT Gateway"
  value       = aws_nat_gateway.main.id
}

output "nat_gateway_public_ip" {
  description = "The public IP address of the NAT Gateway"
  value       = aws_eip.nat.public_ip
}

output "private_route_table_id" {
  description = "The ID of the private route table"
  value       = aws_route_table.private.id
}

output "public_route_table_id" {
  description = "The ID of the public route table"
  value       = aws_route_table.public.id
}

output "eip_id" {
  description = "The ID of the Elastic IP used for the NAT Gateway"
  value       = aws_eip.nat.id
}

output "secretsmanager_vpc_endpoint_id" {
  description = "The ID of the Secrets Manager VPC endpoint (if created)"
  value       = var.create_secretsmanager_endpoint ? aws_vpc_endpoint.secretsmanager[0].id : null
}

output "logs_vpc_endpoint_id" {
  description = "The ID of the CloudWatch Logs VPC endpoint (if created)"
  value       = var.create_logs_endpoint ? aws_vpc_endpoint.logs[0].id : null
}

output "s3_vpc_endpoint_id" {
  description = "The ID of the S3 VPC endpoint (if created)"
  value       = var.create_s3_endpoint ? aws_vpc_endpoint.s3[0].id : null
}

output "dynamodb_vpc_endpoint_id" {
  description = "The ID of the DynamoDB VPC endpoint (if created)"
  value       = var.create_dynamodb_endpoint ? aws_vpc_endpoint.dynamodb[0].id : null
}
