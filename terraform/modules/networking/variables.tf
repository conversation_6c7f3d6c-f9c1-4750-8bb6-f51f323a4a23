variable "app_name" {
  description = "The name of the application"
  type        = string
}

variable "vpc_id" {
  description = "The ID of the existing VPC"
  type        = string
}

variable "public_subnet_id" {
  description = "The ID of the public subnet where the NAT Gateway will be placed"
  type        = string
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs that need internet access via the NAT Gateway"
  type        = list(string)
}

variable "endpoint_security_group_ids" {
  description = "List of security group IDs for VPC endpoints"
  type        = list(string)
}

variable "aws_region" {
  description = "The AWS region where resources will be created"
  type        = string
  default     = "us-east-1"
}

variable "create_secretsmanager_endpoint" {
  description = "Whether to create a VPC endpoint for Secrets Manager"
  type        = bool
  default     = true
}

variable "create_logs_endpoint" {
  description = "Whether to create a VPC endpoint for CloudWatch Logs"
  type        = bool
  default     = true
}

variable "create_s3_endpoint" {
  description = "Whether to create a VPC endpoint for S3"
  type        = bool
  default     = true
}

variable "create_dynamodb_endpoint" {
  description = "Whether to create a VPC endpoint for DynamoDB"
  type        = bool
  default     = true
}

variable "internet_gateway_id" {
  description = "The ID of the Internet Gateway (if not provided, will try to find it automatically)"
  type        = string
  default     = ""
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}
