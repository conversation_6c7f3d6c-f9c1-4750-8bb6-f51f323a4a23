# Networking module for NAT Gateway and VPC endpoints

# Data sources for existing VPC and subnets
data "aws_vpc" "existing" {
  id = var.vpc_id
}

data "aws_subnet" "public" {
  id = var.public_subnet_id
}

# Get the Internet Gateway associated with the VPC
data "aws_internet_gateway" "existing" {
  count = var.internet_gateway_id == "" ? 1 : 0

  filter {
    name   = "attachment.vpc-id"
    values = [var.vpc_id]
  }
}

locals {
  # Use the provided Internet Gateway ID if available, otherwise use the one from the data source
  internet_gateway_id = var.internet_gateway_id != "" ? var.internet_gateway_id : try(data.aws_internet_gateway.existing[0].id, null)
}

# Security group for VPC endpoints
resource "aws_security_group" "vpc_endpoints" {
  name        = "${var.app_name}-vpc-endpoints-sg"
  description = "Security group for VPC endpoints"
  vpc_id      = data.aws_vpc.existing.id

  # Allow all inbound traffic from the VPC CIDR
  ingress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [data.aws_vpc.existing.cidr_block]
    description = "Allow all traffic from the VPC"
  }

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow all outbound traffic"
  }

  tags = merge(var.tags, {
    Name = "${var.app_name}-vpc-endpoints-sg"
  })
}

# Create Elastic IP for NAT Gateway
resource "aws_eip" "nat" {
  domain = "vpc"
  tags = merge(var.tags, {
    Name = "${var.app_name}-nat-eip"
    Description = "Elastic IP for NAT Gateway"
  })
}

# Create NAT Gateway in the public subnet
resource "aws_nat_gateway" "main" {
  allocation_id = aws_eip.nat.id
  subnet_id     = data.aws_subnet.public.id

  tags = merge(var.tags, {
    Name = "${var.app_name}-nat-gateway"
    Description = "NAT Gateway for Lambda internet access"
  })

  # Add dependency to ensure the EIP is created first
  depends_on = [aws_eip.nat]
}

# Create a new route table for private subnets
resource "aws_route_table" "private" {
  vpc_id = data.aws_vpc.existing.id

  # Default route through NAT Gateway for internet access
  route {
    cidr_block = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.main.id
  }

  tags = merge(var.tags, {
    Name = "${var.app_name}-private-route-table"
    Description = "Route table for private subnets with NAT Gateway access"
  })
}

# Associate route table with private subnets
resource "aws_route_table_association" "private_subnets" {
  for_each = toset(var.private_subnet_ids)

  subnet_id      = each.value
  route_table_id = aws_route_table.private.id
}

# Create a new route table for the public subnet where NAT Gateway is located
resource "aws_route_table" "public" {
  vpc_id = data.aws_vpc.existing.id

  # Default route through Internet Gateway for internet access
  dynamic "route" {
    for_each = local.internet_gateway_id != null ? [1] : []
    content {
      cidr_block = "0.0.0.0/0"
      gateway_id = local.internet_gateway_id
    }
  }

  tags = merge(var.tags, {
    Name = "${var.app_name}-public-route-table"
    Description = "Route table for public subnet with Internet Gateway access"
  })
}

# Associate the public route table with the public subnet
resource "aws_route_table_association" "public_subnet" {
  subnet_id      = data.aws_subnet.public.id
  route_table_id = aws_route_table.public.id
}

# Create VPC endpoint for Secrets Manager
resource "aws_vpc_endpoint" "secretsmanager" {
  count = var.create_secretsmanager_endpoint ? 1 : 0

  vpc_id              = data.aws_vpc.existing.id
  service_name        = "com.amazonaws.${var.aws_region}.secretsmanager"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = var.private_subnet_ids

  # Allow all traffic from the VPC
  security_group_ids  = concat(var.endpoint_security_group_ids, [aws_security_group.vpc_endpoints.id])

  # Enable private DNS to ensure the Lambda can use the default endpoint URL
  private_dns_enabled = true

  tags = merge(var.tags, {
    Name = "${var.app_name}-secretsmanager-endpoint"
    Description = "VPC Endpoint for Secrets Manager access"
  })
}

# Create VPC endpoint for CloudWatch Logs
resource "aws_vpc_endpoint" "logs" {
  count = var.create_logs_endpoint ? 1 : 0

  vpc_id              = data.aws_vpc.existing.id
  service_name        = "com.amazonaws.${var.aws_region}.logs"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = var.private_subnet_ids
  security_group_ids  = concat(var.endpoint_security_group_ids, [aws_security_group.vpc_endpoints.id])
  private_dns_enabled = true

  tags = merge(var.tags, {
    Name = "${var.app_name}-logs-endpoint"
    Description = "VPC Endpoint for CloudWatch Logs access"
  })
}

# Create VPC endpoint for S3
resource "aws_vpc_endpoint" "s3" {
  count = var.create_s3_endpoint ? 1 : 0

  vpc_id            = data.aws_vpc.existing.id
  service_name      = "com.amazonaws.${var.aws_region}.s3"
  vpc_endpoint_type = "Gateway"
  route_table_ids   = [aws_route_table.private.id, aws_route_table.public.id]

  tags = merge(var.tags, {
    Name = "${var.app_name}-s3-endpoint"
    Description = "VPC Endpoint for S3 access"
  })
}

# Create VPC endpoint for DynamoDB
resource "aws_vpc_endpoint" "dynamodb" {
  count = var.create_dynamodb_endpoint ? 1 : 0

  vpc_id            = data.aws_vpc.existing.id
  service_name      = "com.amazonaws.${var.aws_region}.dynamodb"
  vpc_endpoint_type = "Gateway"
  route_table_ids   = [aws_route_table.private.id, aws_route_table.public.id]

  tags = merge(var.tags, {
    Name = "${var.app_name}-dynamodb-endpoint"
    Description = "VPC Endpoint for DynamoDB access"
  })
}
