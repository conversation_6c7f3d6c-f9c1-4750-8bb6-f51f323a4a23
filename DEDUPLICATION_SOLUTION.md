# Slack Message Deduplication Solution

## Problem
When a Lambda function is "cold" (not active), it takes time to start up. During this startup time, <PERSON><PERSON><PERSON>'s retry mechanism can send multiple requests for the same message, resulting in duplicate responses to the user.

## Solution
This solution implements a PostgreSQL-based deduplication system that tracks processed Slack messages to prevent duplicate responses.

## Components

### 1. Database Table: `message_deduplication`
- **event_id**: Unique identifier for each Slack event (primary key for deduplication)
- **event_ts**: Slack event timestamp
- **channel_id**: Slack channel ID
- **user_id**: Slack user ID
- **message_text**: The message content
- **processed_at**: When the record was created
- **status**: Processing status (`processing`, `completed`, `failed`)

### 2. Repository: `MessageDeduplicationRepository`
Located in `app2/db/repositories.py`, provides methods to:
- `mark_message_processing()`: Atomically mark a message as being processed
- `mark_message_completed()`: Mark successful completion
- `mark_message_failed()`: Mark failed processing
- `cleanup_old_records()`: Remove old records to prevent table growth

### 3. Integration in `SlackBotApp.process_message()`
The deduplication logic is integrated at the beginning of message processing:

1. **Extract event identifiers** from the Slack event
2. **Check for duplicates** using database atomic insert
3. **Process message** if not a duplicate
4. **Mark completion** or failure status

## How It Works

1. **Message Received**: When a Slack message is received, extract the `event_id`
2. **Atomic Check**: Try to insert a record with status "processing"
   - If successful: Continue processing
   - If fails (duplicate): Skip processing and return
3. **Process Message**: Execute the normal AI response flow
4. **Mark Status**: Update status to "completed" or "failed"

## Key Features

- **Atomic Operations**: Uses database constraints to prevent race conditions
- **Graceful Handling**: Skips duplicates without errors
- **Status Tracking**: Tracks processing status for debugging
- **Cleanup**: Automatic cleanup of old records to prevent table growth
- **Scheduled Message Support**: Handles scheduled messages differently

## Usage

### Automatic Deduplication
The deduplication is automatic and requires no changes to existing code. It activates for:
- Direct messages to the bot
- Channel mentions (@bot)
- App mentions

### Manual Cleanup
Run the cleanup script periodically:

```bash
python app2/scripts/cleanup_deduplication.py --days 7
```

### Database Migration
The solution includes an Alembic migration:

```bash
alembic upgrade 004
```

## Configuration

No additional configuration is required. The system uses:
- Default retention: 7 days
- Automatic status tracking
- PostgreSQL constraints for atomicity

## Benefits

1. **Eliminates Duplicate Responses**: Users no longer receive multiple responses for the same question
2. **Improved User Experience**: Consistent, single responses even during cold starts
3. **Resource Efficiency**: Prevents unnecessary AI processing for duplicate requests
4. **Debugging Support**: Status tracking helps identify processing issues
5. **Scalable**: Uses database constraints for thread-safe operation

## Monitoring

Monitor the `message_deduplication` table for:
- High volume of duplicate attempts (indicates cold start issues)
- Failed processing status (indicates system issues)
- Table growth (schedule cleanup if needed)

## Limitations

- Requires PostgreSQL database
- Adds small latency for database check
- Requires periodic cleanup to prevent table growth
- Only works for non-scheduled messages (scheduled messages bypass deduplication) 