from sqlalchemy import Column, String, Boolean
from app2.db.base_class import Base


class UserPreferences(Base):
    __tablename__ = "user_preferences"

    user_id = Column(String, primary_key=True, index=True)
    show_feedback = Column(Boolean, default=True)
    show_detailed_results = Column(Boolean, default=True)
    show_sql_query = Column(Boolean, default=True)
    show_table_results = Column(Boolean, default=True)
