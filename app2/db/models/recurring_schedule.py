from sqlalchemy import Column, String, Boolean, DateTime, Text, func
from app2.db.base_class import Base
from sqlalchemy.dialects.postgresql import UUID
import uuid


class RecurringSchedule(Base):
    __tablename__ = "recurring_schedules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String, nullable=False, index=True)
    channel_id = Column(String, nullable=False)
    message = Column(Text, nullable=False)
    cron_expression = Column(String, nullable=False)
    timezone = Column(String, nullable=False)
    schedule_type = Column(
        String, nullable=False, default="recurring"
    )  # 'recurring' or 'one_time'
    last_run = Column(DateTime(timezone=True), nullable=True)
    next_run = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(
        DateTime(timezone=True), server_default=func.now(), onupdate=func.now()
    )
    is_active = Column(Boolean, default=True)
    eventbridge_rule_arn = Column(String, nullable=True)
