from sqlalchemy.orm import Session
from app2.db.models.user_preferences import UserPreferences


class UserPreferencesRepository:
    def __init__(self, db: Session):
        self.db = db

    def get_preferences(self, user_id: str) -> UserPreferences:
        """Get user preferences, create with defaults if not exists."""
        preferences = (
            self.db.query(UserPreferences)
            .filter(UserPreferences.user_id == user_id)
            .first()
        )

        if not preferences:
            preferences = UserPreferences(
                user_id=user_id,
                show_feedback=True,
                show_detailed_results=True,
                show_sql_query=True,
                show_table_results=True,
            )
            self.db.add(preferences)
            self.db.commit()
            self.db.refresh(preferences)

        return preferences

    def update_preferences(self, user_id: str, **kwargs) -> UserPreferences:
        """Update user preferences."""
        preferences = self.get_preferences(user_id)

        for key, value in kwargs.items():
            if hasattr(preferences, key):
                setattr(preferences, key, value)

        self.db.commit()
        self.db.refresh(preferences)
        return preferences
