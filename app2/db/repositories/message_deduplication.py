from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError


class MessageDeduplicationRepository:
    def __init__(self, db: Session):
        self.db = db

    def is_message_processed(self, event_id: str) -> bool:
        """Check if a message has already been processed."""
        from app2.models.message_deduplication import MessageDeduplication

        existing = (
            self.db.query(MessageDeduplication)
            .filter(MessageDeduplication.event_id == event_id)
            .first()
        )
        return existing is not None

    def mark_message_processing(
        self,
        event_id: str,
        event_ts: str,
        channel_id: str,
        user_id: str,
        message_text: str = None,
    ) -> bool:
        """Mark a message as being processed. Returns True if successfully marked, False if already exists."""
        from app2.models.message_deduplication import MessageDeduplication

        try:
            dedup_record = MessageDeduplication(
                event_id=event_id,
                event_ts=event_ts,
                channel_id=channel_id,
                user_id=user_id,
                message_text=message_text,
                status="processing",
            )
            self.db.add(dedup_record)
            self.db.commit()
            return True
        except IntegrityError:
            # Record already exists, rollback and return False
            self.db.rollback()
            return False

    def mark_message_completed(self, event_id: str) -> None:
        """Mark a message as completed processing."""
        from app2.models.message_deduplication import MessageDeduplication

        record = (
            self.db.query(MessageDeduplication)
            .filter(MessageDeduplication.event_id == event_id)
            .first()
        )
        if record:
            record.status = "completed"
            self.db.commit()

    def mark_message_failed(self, event_id: str) -> None:
        """Mark a message as failed processing."""
        from app2.models.message_deduplication import MessageDeduplication

        record = (
            self.db.query(MessageDeduplication)
            .filter(MessageDeduplication.event_id == event_id)
            .first()
        )
        if record:
            record.status = "failed"
            self.db.commit()

    def cleanup_old_records(self, days: int = 7) -> int:
        """Clean up deduplication records older than specified days. Returns count of deleted records."""
        from app2.models.message_deduplication import MessageDeduplication
        from datetime import datetime, timedelta

        cutoff_date = datetime.utcnow() - timedelta(days=days)
        deleted_count = (
            self.db.query(MessageDeduplication)
            .filter(MessageDeduplication.processed_at < cutoff_date)
            .delete()
        )
        self.db.commit()
        return deleted_count
