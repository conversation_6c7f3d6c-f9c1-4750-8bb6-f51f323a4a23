from typing import List, Optional
from sqlalchemy.orm import Session
from uuid import UUID

from app2.db.models.recurring_schedule import RecurringSchedule
from app2.schemas.recurring_schedule import (
    RecurringScheduleCreate,
    RecurringScheduleUpdate,
)


class RecurringScheduleRepository:
    def __init__(self, db: Session):
        self.db = db

    def create(self, schedule: RecurringScheduleCreate) -> RecurringSchedule:
        db_schedule = RecurringSchedule(
            user_id=schedule.user_id,
            channel_id=schedule.channel_id,
            message=schedule.message,
            cron_expression=schedule.cron_expression,
            timezone=schedule.timezone,
            schedule_type=schedule.schedule_type,
            is_active=schedule.is_active,
        )
        self.db.add(db_schedule)
        self.db.commit()
        self.db.refresh(db_schedule)
        return db_schedule

    def get(self, schedule_id: UUID) -> Optional[RecurringSchedule]:
        return (
            self.db.query(RecurringSchedule)
            .filter(RecurringSchedule.id == schedule_id)
            .first()
        )

    def get_by_user(self, user_id: str) -> List[RecurringSchedule]:
        return (
            self.db.query(RecurringSchedule)
            .filter(RecurringSchedule.user_id == user_id)
            .all()
        )

    def get_active(self) -> List[RecurringSchedule]:
        return (
            self.db.query(RecurringSchedule).filter(RecurringSchedule.is_active).all()
        )

    def update(
        self, schedule_id: UUID, schedule_update: RecurringScheduleUpdate
    ) -> Optional[RecurringSchedule]:
        db_schedule = self.get(schedule_id)
        if not db_schedule:
            return None

        update_data = schedule_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_schedule, field, value)

        self.db.commit()
        self.db.refresh(db_schedule)
        return db_schedule

    def delete(self, schedule_id: UUID) -> bool:
        db_schedule = self.get(schedule_id)
        if not db_schedule:
            return False

        self.db.delete(db_schedule)
        self.db.commit()
        return True

    def update_rule_arn(
        self, schedule_id: UUID, rule_arn: str
    ) -> Optional[RecurringSchedule]:
        db_schedule = self.get(schedule_id)
        if not db_schedule:
            return None

        db_schedule.eventbridge_rule_arn = rule_arn
        self.db.commit()
        self.db.refresh(db_schedule)
        return db_schedule

    def delete_schedule(self, schedule_id: UUID, user_id: str) -> tuple[bool, str]:
        """Delete a schedule with user validation and EventBridge cleanup."""
        import logging

        try:
            # Get the schedule
            schedule = self.get(schedule_id)
            if not schedule:
                return False, "Schedule not found."

            # Verify ownership
            if schedule.user_id != user_id:
                return False, "You can only delete your own schedules."

            # Delete EventBridge rule if it exists
            if schedule.eventbridge_rule_arn:
                try:
                    from app2.services.eventbridge import EventBridgeService

                    eventbridge_service = EventBridgeService()
                    eventbridge_service.delete_rule(schedule.id)
                    logging.info(f"Deleted EventBridge rule for schedule {schedule_id}")
                except Exception as e:
                    logging.error(f"Error deleting EventBridge rule: {e}")
                    # Continue with database deletion even if EventBridge cleanup fails

            # Delete from database
            success = self.delete(schedule_id)
            if success:
                return True, f"Schedule '{schedule.message[:50]}...' has been deleted."
            else:
                return False, "Failed to delete schedule from database."

        except Exception as e:
            logging.error(f"Error deleting schedule {schedule_id}: {e}")
            return False, f"An error occurred while deleting the schedule: {str(e)}"
