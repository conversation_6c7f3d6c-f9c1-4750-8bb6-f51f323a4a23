from app2.db.base_class import Base
from app2.db.models.recurring_schedule import RecurringSchedule
from app2.db.models.user_preferences import UserPreferences
from app2.models.feedback import Feedback  # noqa
from app2.models.message_deduplication import MessageDeduplication  # noqa

# Import all models here that should be included in 'Base.metadata.create_all()'
__all__ = ["Base", "UserPreferences", "RecurringSchedule", "MessageDeduplication"]
