from sqlalchemy import Column, Inte<PERSON>, String, Boolean, Text, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


class Feedback(Base):
    """Model for storing user feedback on chatbot responses."""

    __tablename__ = "feedback"

    id = Column(Integer, primary_key=True)
    message_ts = Column(String, nullable=False, unique=True)  # Slack message timestamp
    channel_id = Column(String, nullable=False)  # Slack channel ID
    user_id = Column(String, nullable=False)  # Slack user ID
    chatbot_response = Column(Text, nullable=False)  # The response that was rated
    user_question = Column(Text, nullable=True)  # The user's question
    s3_query_id = Column(String, nullable=True)  # ID to link to S3 stored query results
    reaction = Column(
        Boolean, nullable=False
    )  # True for thumbs up, False for thumbs down
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class MessageDeduplication(Base):
    """Model for tracking processed Slack messages to prevent duplicates."""

    __tablename__ = "message_deduplication"

    id = Column(Integer, primary_key=True)
    event_id = Column(String, nullable=False, unique=True)  # Slack event ID
    event_ts = Column(String, nullable=False)  # Slack event timestamp
    channel_id = Column(String, nullable=False)  # Slack channel ID
    user_id = Column(String, nullable=False)  # Slack user ID
    message_text = Column(Text, nullable=True)  # The message text
    processed_at = Column(DateTime(timezone=True), server_default=func.now())
    status = Column(
        String, nullable=False, default="processing"
    )  # processing, completed, failed
