"""add user preferences table

Revision ID: 2024_05_14_1800_add_user_preferences
Revises:
Create Date: 2024-05-14 18:00:00.000000

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "2024_05_14_1800_add_user_preferences"
down_revision = None  # Update this if you have previous migrations
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "user_preferences",
        sa.Column("user_id", sa.String(), nullable=False),
        sa.<PERSON>umn("show_feedback", sa.<PERSON>(), nullable=True, server_default="true"),
        sa.<PERSON>umn(
            "show_detailed_results", sa.<PERSON>(), nullable=True, server_default="true"
        ),
        sa.<PERSON>umn("show_sql_query", sa.<PERSON>(), nullable=True, server_default="true"),
        sa.<PERSON>KeyConstraint("user_id"),
    )
    op.create_index(
        op.f("ix_user_preferences_user_id"),
        "user_preferences",
        ["user_id"],
        unique=True,
    )


def downgrade() -> None:
    op.drop_index(op.f("ix_user_preferences_user_id"), table_name="user_preferences")
    op.drop_table("user_preferences")
