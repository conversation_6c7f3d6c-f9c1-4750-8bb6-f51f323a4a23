from sqlalchemy.orm import Session
from app2.db.models import Feedback
from app2.schemas.feedback import FeedbackCreate


class FeedbackRepository:
    """Repository for handling feedback database operations."""

    def __init__(self, db: Session):
        self.db = db

    def create_feedback(self, feedback: FeedbackCreate) -> Feedback:
        """Create a new feedback record."""
        db_feedback = Feedback(
            message_ts=feedback.message_ts,
            channel_id=feedback.channel_id,
            user_id=feedback.user_id,
            chatbot_response=feedback.chatbot_response,
            user_question=feedback.user_question,
            s3_query_id=feedback.s3_query_id,
            reaction=feedback.reaction,
        )
        self.db.add(db_feedback)
        self.db.commit()
        self.db.refresh(db_feedback)
        return db_feedback

    def get_feedback_by_message_ts(self, message_ts: str) -> Feedback:
        """Get feedback by message timestamp."""
        return self.db.query(Feedback).filter(Feedback.message_ts == message_ts).first()

    def update_feedback(self, message_ts: str, reaction: bool) -> Feedback:
        """Update feedback reaction."""
        feedback = self.get_feedback_by_message_ts(message_ts)
        if feedback:
            feedback.reaction = reaction
            self.db.commit()
            self.db.refresh(feedback)
        return feedback

    @staticmethod
    def has_user_voted(db: Session, message_ts: str) -> bool:
        """Check if a message has already been voted on."""
        return db.query(Feedback).filter(Feedback.message_ts == message_ts).count() > 0
