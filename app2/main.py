import logging
import os
import sys
import json
import traceback

import uvicorn
from fastapi import <PERSON>AP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from mangum import Mangum
from slack_sdk import WebClient

from app2.checks.routes import router as checks_router
from app2.routes import router
from app2.slack.handler import handler as slack_handler
from app2.table.routes import router as table_router
from app2.api.api import api_router
from app2.slack.slack_app import SlackBotApp
from app2.utils import is_running_in_lambda


# Simple logging configuration for Lambda
def setup_logging():
    root = logging.getLogger()
    if root.handlers:
        for handler in root.handlers:
            root.removeHandler(handler)

    root.setLevel(logging.INFO)
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter("%(levelname)s: %(message)s"))
    root.addHandler(handler)
    return root


# Setup logging
logger = setup_logging()

# Determine if running in Lambda with API Gateway
is_lambda = is_running_in_lambda()
stage_name = os.environ.get("STAGE_NAME", "dev")

# Create FastAPI app with the proper root_path if in Lambda
if is_lambda:
    app = FastAPI(root_path=f"/{stage_name}")
else:
    app = FastAPI()

# Mount static files directory
static_dir = os.path.join(os.path.dirname(__file__), "static")
app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Allow CORS (adjust origins as needed)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Replace "*" with specific origins in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(router)
app.include_router(checks_router)
app.include_router(table_router)
app.include_router(api_router)


@app.get("/")
async def root():
    return {"message": "SlackGenie API is running"}


# Add error handling
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"Global error handler caught: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"message": "Internal server error"},
    )


# Create a directory for Gradio assets if it doesn't exist
# Check if we're running in Lambda environment to use appropriate directory
if is_lambda:
    assets_dir = os.path.join("/tmp", "assets")
else:
    assets_dir = os.path.join(static_dir, "assets")

os.makedirs(assets_dir, exist_ok=True)


# Add a catch-all route for assets that Gradio might be requesting
@app.get("/assets/{asset_path:path}")
async def get_asset(asset_path: str):
    # First check if it exists in our static directory
    static_asset_path = os.path.join(assets_dir, asset_path)
    if os.path.exists(static_asset_path):
        return FileResponse(static_asset_path)

    # If asset not found, log it for debugging
    print(f"Asset not found: {asset_path}")
    return {"error": "Asset not found"}


# Set port for local development
port = 8080 if not is_lambda else 5000
reload_true = is_lambda is False
print(f"Reload: {reload_true}")


# Define the Lambda handler function
def lambda_handler(event, context):
    # Force immediate logging
    print("=== LAMBDA FUNCTION STARTED ===")
    sys.stdout.flush()

    try:
        # Log the raw event
        print(f"Raw event: {json.dumps(event)}")
        sys.stdout.flush()

        # Check if this is an EventBridge scheduled event
        if event.get("is_scheduled") and event.get("type") == "message":
            print("Detected EventBridge scheduled event")
            sys.stdout.flush()

            try:
                # Initialize the Slack app and client
                from app2.config import Settings

                settings = Settings()
                slack_app = SlackBotApp()
                client = WebClient(token=settings.slack_bot_token)

                # Process the scheduled message
                slack_app.process_message(client, event)
                return {
                    "statusCode": 200,
                    "body": json.dumps(
                        {"message": "Scheduled message processed successfully"}
                    ),
                }
            except Exception as e:
                print(f"Error processing scheduled message: {str(e)}")
                print(f"Traceback: {traceback.format_exc()}")
                sys.stdout.flush()
                return {"statusCode": 500, "body": json.dumps({"error": str(e)})}

        # Check if this is a Slack event
        elif event.get("headers", {}).get("x-slack-request-timestamp") or event.get(
            "headers", {}
        ).get("X-Slack-Request-Timestamp"):
            print("Detected Slack event, using Slack handler")
            sys.stdout.flush()
            # Use the Slack handler for Slack events
            try:
                response = slack_handler.handle(event, context)
                print(f"Slack handler response: {json.dumps(response)}")
                sys.stdout.flush()
                return response
            except Exception as e:
                print(f"Error in Slack handler: {str(e)}")
                print(f"Traceback: {traceback.format_exc()}")
                sys.stdout.flush()
                return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
        else:
            print("Using Mangum handler for non-Slack events")
            sys.stdout.flush()
            # Use Mangum for other API requests (including Slack interactions)
            mangum_handler = Mangum(app)
            try:
                response = mangum_handler(event, context)
                print(f"Mangum handler response: {json.dumps(response)}")
                sys.stdout.flush()
                return response
            except Exception as e:
                print(f"Error in Mangum handler: {str(e)}")
                print(f"Traceback: {traceback.format_exc()}")
                sys.stdout.flush()
                return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
    except Exception as e:
        print(f"Unexpected error in lambda_handler: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        sys.stdout.flush()
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}


# For local development
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=port)

# Export the handler for AWS Lambda
handler = lambda_handler
