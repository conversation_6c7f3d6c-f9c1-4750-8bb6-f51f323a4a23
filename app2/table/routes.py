import boto3
from fastapi import <PERSON>Router, HTTPException, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from botocore.exceptions import ClientError
import logging

from app2.config import Settings

router = APIRouter()

# Initialize S3 client
s3_client = boto3.client("s3")

# Initialize Jinja2Templates
templates = Jinja2Templates(directory="app2/templates")

# Create a Settings instance
app_config = Settings()

# Configure logging
logger = logging.getLogger(__name__)


def generate_presigned_url(query_id: str, expiration: int = 604800) -> str:
    """
    Generate a presigned URL for an S3 object

    Args:
        query_id: The query ID of the file
        expiration: URL expiration time in seconds (default 1 week)

    Returns:
        str: Presigned URL for the file
    """
    try:
        logger.info(f"Generating presigned URL for query_id: {query_id}")
        logger.info(f"Using S3 bucket: {app_config.s3_bucket_name}")

        url = s3_client.generate_presigned_url(
            "get_object",
            Params={
                "Bucket": app_config.s3_bucket_name,
                "Key": f"results/{query_id}.json",
            },
            ExpiresIn=expiration,
        )
        logger.info(f"Successfully generated presigned URL: {url}")
        return url
    except Exception as e:
        logger.error(f"Error generating presigned URL: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Error generating presigned URL: {str(e)}"
        )


@router.get("/table", response_class=HTMLResponse)
async def get_table(request: Request, query_id: str):
    """
    Get the table view for a specific query result.

    Args:
        request: The FastAPI request object
        query_id: The ID of the query to display

    Returns:
        HTMLResponse: The table view
    """
    try:
        logger.info(f"Processing table request for query_id: {query_id}")

        # Generate presigned URL for the S3 file
        presigned_url = generate_presigned_url(query_id)

        # Fetch the JSON data from S3 using the presigned URL
        import requests

        logger.info(f"Fetching data from presigned URL: {presigned_url}")
        response = requests.get(presigned_url)

        if response.status_code != 200:
            logger.error(f"Failed to fetch data. Status code: {response.status_code}")
            logger.error(f"Response content: {response.text}")
            raise HTTPException(status_code=404, detail="Query result not found")

        data = response.json()
        logger.info("Successfully parsed JSON data")

        # Use the existing template with the new data structure
        return templates.TemplateResponse(
            "table_detailed.html",
            {
                "request": request,
                "query": data.get("query", ""),
                "data_description": data.get("data_description", ""),
                "columns": data.get("columns", []),
                "data": data.get("data", []),
            },
        )

    except ClientError as e:
        logger.error(f"AWS ClientError: {str(e)}")
        raise HTTPException(status_code=404, detail="Query result not found")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
