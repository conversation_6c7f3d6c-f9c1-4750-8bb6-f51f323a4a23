import os
import logging

logger = logging.getLogger(__name__)


def is_running_in_lambda():
    """
    Detect if we're running in AWS Lambda environment.
    Uses multiple environment variables for robust detection.

    Returns:
        bool: True if running in AWS Lambda, False otherwise
    """
    # Check multiple Lambda-specific environment variables
    lambda_indicators = [
        os.environ.get("AWS_LAMBDA_FUNCTION_NAME"),  # Always present in Lambda
        os.environ.get("AWS_LAMBDA_FUNCTION_VERSION"),  # Always present in Lambda
        os.environ.get("LAMBDA_RUNTIME_DIR"),  # Present in newer Lambda runtimes
        os.environ.get("AWS_EXECUTION_ENV", "").startswith(
            "AWS_Lambda_"
        ),  # Original check
    ]

    # If any of these indicators are present, we're in Lambda
    is_lambda = any(lambda_indicators)

    # Log the detection result for debugging (only once per import)
    if not hasattr(is_running_in_lambda, "_logged"):
        logging.info("Lambda environment detection:")
        logging.info(
            f"  AWS_LAMBDA_FUNCTION_NAME: {os.environ.get('AWS_LAMBDA_FUNCTION_NAME', 'Not set')}"
        )
        logging.info(
            f"  AWS_LAMBDA_FUNCTION_VERSION: {os.environ.get('AWS_LAMBDA_FUNCTION_VERSION', 'Not set')}"
        )
        logging.info(
            f"  AWS_EXECUTION_ENV: {os.environ.get('AWS_EXECUTION_ENV', 'Not set')}"
        )
        logging.info(
            f"  LAMBDA_RUNTIME_DIR: {os.environ.get('LAMBDA_RUNTIME_DIR', 'Not set')}"
        )
        logging.info(f"  Detected as Lambda: {is_lambda}")
        is_running_in_lambda._logged = True

    return is_lambda
