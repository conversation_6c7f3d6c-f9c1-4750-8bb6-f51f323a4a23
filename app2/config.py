import logging
import os

import certifi
import tomllib
from dotenv import load_dotenv
from pydantic import ConfigDict
from pydantic_settings import BaseSettings
from typing import Optional

from app2.handlesecrets import get_secret
from app2.utils import is_running_in_lambda

logger = logging.getLogger(__name__)

load_dotenv()

os.environ["SSL_CERT_FILE"] = certifi.where()
os.environ["SSL_CERT_DIR"] = os.path.dirname(certifi.where())

# Constants
NUMBER_ROWS_TRUNCATED = 20
DEFAULT_SECRETS_ARN = "arn:aws:secretsmanager:us-east-1:989284569019:secret:slackgenie/dev/app-secrets-SOd2JS"  # noqa: E501
RDS_SECRETS_ARN = "arn:aws:secretsmanager:us-east-1:989284569019:secret:slackgenie/dev/db-credentials-eBpy8G"  # noqa: E501

config_to_display = [
    "APP_VERSION",
    "BASE_URL",
    "DATABRICKS_HOST",
]

secrets_to_display = [
    "ACCESS_TOKEN",
    "DATABASE_URL",
    "DATABRICKS_HOST",
    "OPENAI_API_KEY",
    "SLACK_APP_TOKEN",
    "SLACK_BOT_ID",
    "SLACK_BOT_TOKEN",
    "SLACK_SIGNING_SECRET",
    "SLACK_WEBHOOK",
    "SPACE_ID",
    "LAMBDA_ARN",
    "EVENTBRIDGE_ROLE_ARN",
]

non_optional_dotenv = [
    "ACCESS_TOKEN",
    "DATABASE_URL",
    "DATABRICKS_HOST",
    "OPENAI_API_KEY",
    "SLACK_APP_TOKEN",
    "SLACK_BOT_ID",
    "SLACK_BOT_TOKEN",
    "SLACK_SIGNING_SECRET",
    "SLACK_WEBHOOK",
    "SPACE_ID",
    "LAMBDA_ARN",
    "EVENTBRIDGE_ROLE_ARN",
]


def get_values(values: dict) -> dict:
    new_dict = {}
    for x in values:
        if x in secrets_to_display:
            # Convert value to string before slicing
            value_str = str(values[x])
            new_dict[x] = f"{value_str[:2]}...{value_str[-4:]}"
        elif x in config_to_display:
            new_dict[x] = values[x]
        else:
            pass
    return new_dict


PROJECT_FILE = os.path.join(os.path.dirname(__file__), "../pyproject.toml")
if not os.path.exists(PROJECT_FILE):
    PROJECT_FILE = "./pyproject.toml"

with open(PROJECT_FILE, mode="rb") as fp:
    data = tomllib.load(fp)
    APP_VERSION = data["tool"]["poetry"]["version"]
    os.environ["APP_VERSION"] = APP_VERSION
    print("APP_VERSION: ", APP_VERSION)


class Settings(BaseSettings):
    # App Envs
    app_version: str = "0.9.19"
    base_url: str

    # Database Envs
    database_url: str

    # OpenAI Envs
    openai_api_key: Optional[str] = None

    # Slack Envs
    slack_bot_token: str
    slack_signing_secret: str
    slack_app_token: Optional[str] = None
    slack_bot_id: str
    slack_webhook: str

    # Databricks Envs
    databricks_host: str
    access_token: str
    space_id: str
    number_rows_truncated: str = os.getenv("NUMBER_ROWS_TRUNCATED", "20")

    # S3 Envs
    s3_bucket_name: str = os.getenv("S3_BUCKET_NAME", "distillgenie")

    # AWS settings - Optional during local development
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None
    aws_region: str = "us-east-1"
    lambda_arn: Optional[str] = None
    eventbridge_role_arn: Optional[str] = None

    model_config = ConfigDict(extra="allow", env_file=".env", env_file_encoding="utf-8")

    def __init__(self, **kwargs):
        # Only fetch secrets from AWS Secrets Manager if running in Lambda
        is_lambda = is_running_in_lambda()
        logger.info(f"Running in Lambda environment: {is_lambda}")

        if is_lambda:
            # Get secrets from AWS Secrets Manager
            secrets = get_secret(DEFAULT_SECRETS_ARN)
            logger.info(
                f"Retrieved secrets from AWS Secrets Manager: {list(secrets.keys())}"
            )
            db_secrets = get_secret(RDS_SECRETS_ARN)
            logger.info(f"Retrieved db_secrets from AWS SM: {list(db_secrets.keys())}")
            # Update kwargs with secrets
            kwargs.update(secrets)
            kwargs.update(db_secrets)
            logger.info(
                f"Updated kwargs with secrets. Keys in kwargs: {list(kwargs.keys())}"
            )

        # Call parent class constructor
        super().__init__(**kwargs)

    def get_env(self) -> dict:
        # Check if we're in Lambda environment
        is_lambda = is_running_in_lambda()

        if is_lambda:
            # In Lambda, use instance variables instead of os.environ
            env_values = {
                "ACCESS_TOKEN": self.access_token,
                "DATABASE_URL": self.database_url,
                "DATABRICKS_HOST": self.databricks_host,
                "OPENAI_API_KEY": self.openai_api_key,
                "SLACK_APP_TOKEN": self.slack_app_token,
                "SLACK_BOT_ID": self.slack_bot_id,
                "SLACK_BOT_TOKEN": self.slack_bot_token,
                "SLACK_SIGNING_SECRET": self.slack_signing_secret,
                "SLACK_WEBHOOK": self.slack_webhook,
                "SPACE_ID": self.space_id,
                "LAMBDA_ARN": self.lambda_arn,
                "EVENTBRIDGE_ROLE_ARN": self.eventbridge_role_arn,
            }
        else:
            # In local environment, use os.environ
            env_values = dict(os.environ)

        return get_values(env_values)

    def get_config(self) -> dict:
        config_file_values = dict(globals(), **locals())
        return get_values(config_file_values)

    def get_config_status(self) -> dict:
        status = "OK"
        errors = ""
        is_lambda = is_running_in_lambda()

        for x in non_optional_dotenv:
            if is_lambda:
                # In Lambda, check instance variables
                value = getattr(self, x.lower(), None)
            else:
                # In local environment, check os.environ
                value = os.getenv(x)

            if value is None or value == "":
                status = "ERROR"
                errors = errors + f"missing {x}; "

        return {
            "CONFIG_STATUS": status,
            "ERRORS": errors,
        }
