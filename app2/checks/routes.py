from typing import ClassVar

from fastapi import APIRouter, status
from pydantic import BaseModel

from app2.config import Settings

router = APIRouter()

# Create a Settings instance
app_config = Settings()


class HealthCheck(BaseModel):
    """Response model to validate and return when performing a health check."""

    status: str = "OK"


@router.get(
    "/health",
    tags=["healthcheck"],
    summary="Perform a Health Check",
    response_description="Return HTTP Status Code 200 (OK)",
    status_code=status.HTTP_200_OK,
    response_model=HealthCheck,
)
def get_health() -> HealthCheck:
    """
    ## Perform a Health Check
    Endpoint to perform a healthcheck on. This endpoint can primarily be used Docker
    to ensure a robust container orchestration and management is in place. Other
    services which rely on proper functioning of the API service will not deploy if this
    endpoint returns any other HTTP status code except 200 (OK).
    Returns:
        HealthCheck: Returns a JSON response with the health status
    """
    return HealthCheck(status="OK")


class VersionCheck(BaseModel):
    """Response model to validate and return when performing a health check."""

    status: str = "OK"
    version: str = f"v{app_config.app_version}"


@router.get(
    "/version",
    tags=["version-check"],
    summary="Perform a Version Check",
    response_description="Return HTTP Status Code 200 (OK)",
    status_code=status.HTTP_200_OK,
    response_model=VersionCheck,
)
def get_version() -> VersionCheck:
    """
    ## Perform a Version Check
    Endpoint to perform a version-check on. This endpoint can primarily be used to
    Validate that we have the right version on the website and API.
    Returns:
        VersionCheck: Returns a JSON response with the health status
    """
    return VersionCheck(status="OK")


class ConfigCheck(BaseModel):
    """Response model to validate and return when performing a health check."""

    statusDict: ClassVar[dict] = app_config.get_config_status()
    status: str = statusDict["CONFIG_STATUS"]
    errors: str = statusDict["ERRORS"]
    env: dict = app_config.get_env()
    conf: dict = app_config.get_config()


@router.get(
    "/config",
    tags=["config-check"],
    summary="Perform a Config Check",
    response_description="Return HTTP Status Code 200 (OK)",
    status_code=status.HTTP_200_OK,
    response_model=ConfigCheck,
)
def get_config() -> ConfigCheck:
    """
    ## Perform a Version Check
    Endpoint to perform a version-check on. This endpoint can primarily be used to
    Validate that we have the right version on the website and API.
    Returns:
        ConfigCheck: Returns a JSON response with the config status
    """
    return ConfigCheck()
