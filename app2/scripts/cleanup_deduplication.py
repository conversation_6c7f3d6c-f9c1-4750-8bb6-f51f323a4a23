#!/usr/bin/env python3
"""
Cleanup script for message deduplication records.

This script removes deduplication records older than a specified number of days
to prevent the table from growing indefinitely.
"""

import logging
import sys

from app2.db.session import SessionLocal
from app2.db.repositories import MessageDeduplicationRepository

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

logger = logging.getLogger(__name__)


def cleanup_old_deduplication_records(days: int = 7) -> None:
    """
    Clean up deduplication records older than specified days.

    Args:
        days: Number of days to keep records (default: 7)
    """
    try:
        with SessionLocal() as db:
            repo = MessageDeduplicationRepository(db)
            deleted_count = repo.cleanup_old_records(days=days)

        logger.info(
            f"Successfully cleaned up {deleted_count} old deduplication records (older than {days} days)"
        )

    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(
        description="Clean up old message deduplication records"
    )
    parser.add_argument(
        "--days",
        type=int,
        default=7,
        help="Number of days to keep records (default: 7)",
    )

    args = parser.parse_args()

    logger.info(
        f"Starting cleanup of deduplication records older than {args.days} days"
    )
    cleanup_old_deduplication_records(args.days)
    logger.info("Cleanup completed successfully")
