import logging
import ssl
from typing import Callable, Dict, List
from uuid import UUID

import certifi
from slack_bolt import App
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from app2.chatbot.core.graph import Graph
from app2.config import Settings
from app2.resources.prompt_templates import Slack<PERSON>romptTemplate
from app2.db.session import <PERSON><PERSON>oc<PERSON>
from app2.db.repositories import (
    UserPreferencesRepository,
    MessageDeduplicationRepository,
)
from app2.crud import feedback as feedback_crud
from app2.schemas.feedback import FeedbackCreate
from app2.services.eventbridge import EventBridgeService
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.schemas.recurring_schedule import RecurringScheduleCreate

# Create a Settings instance
app_config = Settings()

# Configure SSL context with certifi CA certificates
ssl_context = ssl.create_default_context(cafile=certifi.where())
ssl_context.check_hostname = True
ssl_context.verify_mode = ssl.CERT_REQUIRED

# Initialize WebClient with SSL context
web_client = WebClient(token=app_config.slack_bot_token, ssl=ssl_context)


class SlackBotApp:
    app: App

    def __init__(self) -> None:
        self.app = App(
            process_before_response=True,
            raise_error_for_unhandled_request=True,
            signing_secret=app_config.slack_signing_secret,
            token=app_config.slack_bot_token,
        )
        self._register_handlers()
        SlackBotApp.app = self.app

    def _register_handlers(self) -> None:
        """Register event handlers for Slack interactions."""

        self.app.action("toggle_sql_details")(
            ack=self.ack, lazy=[self.toggle_sql_details]
        )

        self.app.action("toggle_feedback")(
            ack=self.ack, lazy=[self.handle_toggle_feedback]
        )

        self.app.action("toggle_detailed_results")(
            ack=self.ack, lazy=[self.handle_toggle_detailed_results]
        )

        self.app.action("toggle_table_results")(
            ack=self.ack, lazy=[self.handle_toggle_table_results]
        )

        self.app.action("toggle_sql_query")(
            ack=self.ack, lazy=[self.handle_toggle_sql_query]
        )

        self.app.action("view_detailed_results")(
            ack=self.ack, lazy=[self.view_detailed_results]
        )

        # Register slash command for scheduling messages
        self.app.command("/schedule_msg")(ack=self.ack, lazy=[self.handle_schedule_msg])

        # Register slash command for listing scheduled messages
        self.app.command("/list_schedules")(
            ack=self.ack, lazy=[self.handle_list_schedules]
        )

        # Register slash command for deleting scheduled messages
        self.app.command("/delete_schedule")(
            ack=self.ack, lazy=[self.handle_delete_schedule]
        )

        # Register button action for deleting schedules from the list
        self.app.action("delete_schedule_button")(
            ack=self.ack, lazy=[self.handle_delete_schedule_button]
        )

        # Add handlers for feedback actions
        self.app.action("feedback_thumbs_up")(ack=self.ack, lazy=[self.handle_feedback])
        self.app.action("feedback_thumbs_down")(
            ack=self.ack, lazy=[self.handle_feedback]
        )

        self.app.event("app_home_opened")(ack=self.ack, lazy=[self.display_home])
        self.app.event("app_mention")(ack=self.ack, lazy=[self.process_message])

        # Add a specific handler for scheduled messages
        self.app.message("Running scheduled message:")(
            ack=self.ack, lazy=[self.process_message]
        )

        # Handle direct messages
        self.app.event("message", matchers=[self.is_dm])(
            ack=self.ack, lazy=[self.process_message]
        )

        self.app.event("message")(ack=self.ack, lazy=[self.log_unhandled_message])
        self.app.error(self.log_error)

    @staticmethod
    def ack(ack: Callable) -> None:
        """Acknowledge the event to prevent Slack from resending it."""
        ack()

    def toggle_sql_details(self, body: Dict, client: WebClient) -> None:
        """Toggle entre mostrar y ocultar los detalles del SQL."""
        action_value = body["actions"][0]["value"]
        channel = body["channel"]["id"]
        message_ts = body["message"]["ts"]
        parts = action_value.split(":", 1)
        action = parts[0]
        sql_query = parts[1] if len(parts) > 1 else ""

        try:
            # Get current blocks from the message
            result = client.conversations_history(
                channel=channel, latest=message_ts, limit=1, inclusive=True
            )
            current_blocks = (
                result["messages"][0].get("blocks", []) if result["messages"] else []
            )

            # Find and remove any existing SQL query blocks and toggle button
            filtered_blocks = [
                block
                for block in current_blocks
                if not (
                    (
                        block.get("type") == "section"
                        and block.get("text", {}).get("text", "").startswith("```sql")
                    )
                    or (
                        block.get("type") == "actions"
                        and block.get("elements", [])
                        and any(
                            element.get("action_id") == "toggle_sql_details"
                            for element in block.get("elements", [])
                        )
                    )
                )
            ]

            # Add new SQL blocks based on action
            if action == "show":
                filtered_blocks.extend(
                    [
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"```sql\n\n{sql_query}\n```",
                            },
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "▲ Hide Executed Query",
                                        "emoji": True,
                                    },
                                    "action_id": "toggle_sql_details",
                                    "value": f"hide:{sql_query}",
                                }
                            ],
                        },
                    ]
                )
            else:
                filtered_blocks.append(
                    {
                        "type": "actions",
                        "elements": [
                            {
                                "type": "button",
                                "text": {
                                    "type": "plain_text",
                                    "text": "▼ Show Executed Query",
                                    "emoji": True,
                                },
                                "action_id": "toggle_sql_details",
                                "value": f"show:{sql_query}",
                            }
                        ],
                    }
                )

            # Update the message with all blocks
            client.chat_update(channel=channel, ts=message_ts, blocks=filtered_blocks)
        except SlackApiError as e:
            logging.error(f"Error al toggle SQL details: {e}")

    def display_home(self, client: WebClient, event: Dict) -> None:
        """Show the home view when the user opens the app."""
        user_id = event.get("user")

        # Get user preferences
        with SessionLocal() as db:
            repo = UserPreferencesRepository(db)
            preferences = repo.get_preferences(user_id)

        # Define the checkbox options
        feedback_option = {
            "text": {
                "type": "plain_text",
                "text": "Show feedback buttons (👍/👎)",
                "emoji": True,
            },
            "value": "show_feedback",
        }

        detailed_results_option = {
            "text": {
                "type": "plain_text",
                "text": "Show 'View Detailed Results' button",
                "emoji": True,
            },
            "value": "show_detailed_results",
        }

        sql_query_option = {
            "text": {
                "type": "plain_text",
                "text": "Show SQL query button",
                "emoji": True,
            },
            "value": "show_sql_query",
        }

        table_results_option = {
            "text": {
                "type": "plain_text",
                "text": "Show table results in conversation",
                "emoji": True,
            },
            "value": "show_table_results",
        }

        try:
            client.views_publish(
                user_id=event.get("user"),
                view={
                    "type": "home",
                    "callback_id": "home_view",
                    "blocks": [
                        # Welcome header
                        {
                            "type": "header",
                            "text": {
                                "type": "plain_text",
                                "text": SlackPromptTemplate.home_header_plain_text_prompt.template,
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": SlackPromptTemplate.home_section_mrkdwn_prompt.template,
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": " ",
                            },
                        },
                        {"type": "divider"},
                        # Demo section
                        {
                            "type": "header",
                            "text": {
                                "type": "plain_text",
                                "text": SlackPromptTemplate.home_demo_header_prompt.template,
                                "emoji": True,
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": SlackPromptTemplate.home_demo_description_prompt.template,
                            },
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "View Interactive Demo",
                                        "emoji": True,
                                    },
                                    "url": "https://app.supademo.com/demo/cmawu0yhr6vp6ho3rlnpi7pg0",
                                    "style": "primary",
                                }
                            ],
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": " ",
                            },
                        },
                        {"type": "divider"},
                        # Ways to Interact section
                        {
                            "type": "header",
                            "text": {
                                "type": "plain_text",
                                "text": "Ways to Interact with DistillGenie",
                                "emoji": True,
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": "*1. Direct Messages* 📱\nStart a private conversation with DistillGenie by clicking the 'Messages' tab.",
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": "*2. Channel Mentions* 💬\nAdd @DistillGenie to any channel and mention it with your question. Everyone in the channel will see the response.\nExample: `@DistillGenie show me sales data for last month`",
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": "*3. Scheduled Queries* ⏰\nUse the `/schedule_msg` command to set up automated queries that run at specific times.\nExample: `/schedule_msg show daily sales report at 9:00 AM every Monday`\n\n• `/list_schedules` - View all your active scheduled messages\n• `/delete_schedule <id>` - Delete a specific scheduled message",
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": " ",
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": " ",
                            },
                        },
                        {"type": "divider"},
                        # Feature Settings section
                        {
                            "type": "header",
                            "text": {
                                "type": "plain_text",
                                "text": "Feature Settings",
                                "emoji": True,
                            },
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": "Configure which features you want to see in responses:",
                            },
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "checkboxes",
                                    "action_id": "toggle_feedback",
                                    "options": [feedback_option],
                                    **(
                                        {"initial_options": [feedback_option]}
                                        if preferences.show_feedback
                                        else {}
                                    ),
                                }
                            ],
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "checkboxes",
                                    "action_id": "toggle_detailed_results",
                                    "options": [detailed_results_option],
                                    **(
                                        {"initial_options": [detailed_results_option]}
                                        if preferences.show_detailed_results
                                        else {}
                                    ),
                                }
                            ],
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "checkboxes",
                                    "action_id": "toggle_table_results",
                                    "options": [table_results_option],
                                    **(
                                        {"initial_options": [table_results_option]}
                                        if preferences.show_table_results
                                        else {}
                                    ),
                                }
                            ],
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "checkboxes",
                                    "action_id": "toggle_sql_query",
                                    "options": [sql_query_option],
                                    **(
                                        {"initial_options": [sql_query_option]}
                                        if preferences.show_sql_query
                                        else {}
                                    ),
                                }
                            ],
                        },
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": " ",
                            },
                        },
                        {"type": "divider"},
                        # Footer
                        {
                            "type": "context",
                            "elements": [
                                {
                                    "type": "mrkdwn",
                                    "text": SlackPromptTemplate.home_footer_prompt.template,
                                }
                            ],
                        },
                    ],
                },
            )
        except SlackApiError as e:
            logging.error(f"Error displaying home view: {e}")

    @staticmethod
    def log_unhandled_message(event: Dict) -> None:
        """Log received messages that do not trigger an action."""

        logging.info(f"Unhandled message: {event}")

    @staticmethod
    def format_history_as_prompt(history: list) -> str:
        """
        Convert Slack message history into a structured prompt for an AI model,
        ensuring messages are paired correctly and handling errors gracefully.
        """
        if not isinstance(history, list):
            raise ValueError("History must be a list of message dictionaries.")

        prompt = """
        Below is a Previous Conversation/Interaction between a Human and an AI assistant:
        """
        pair_counter = 1
        temp_message = None
        formatted_pairs = []

        try:
            for message in history:
                if (
                    not isinstance(message, dict)
                    or "role" not in message
                    or "content" not in message
                ):
                    continue

                role = "Human" if message["role"] == "user" else "AI"
                formatted_message = (
                    f"{role} message {pair_counter}: {message['content']}\n"
                )

                if role == "Human":
                    temp_message = formatted_message
                else:
                    if temp_message:
                        formatted_pairs.append(temp_message)
                    formatted_pairs.append(formatted_message + "\n")
                    pair_counter += 1

            if temp_message and len(formatted_pairs) % 2 != 0:
                formatted_pairs.append(
                    f"AI message {pair_counter}: [No response recorded]\n\n"
                )

        except Exception as e:
            return f"Error formatting conversation history: {str(e)}"

        return prompt.strip() + "\n\n" + "".join(formatted_pairs)

    @staticmethod
    def get_message_history(
        client: WebClient, user_id: str, channel: str, limit: int = 10
    ) -> List[Dict]:
        """Retrieve the last messages in a conversation."""

        try:
            messages = client.conversations_history(channel=channel, limit=limit).get(
                "messages", []
            )
            history = [
                {
                    "role": "user" if msg.get("user") == user_id else "assistant",
                    "content": msg.get("text", "")
                    if msg.get("subtype") is None
                    else "",
                }
                for msg in messages
            ]
            history.reverse()

            try:
                formatted_prompt = SlackBotApp.format_history_as_prompt(history)
                return formatted_prompt
            except ValueError as ve:
                logging.error(f"Formatting context/memory prompt error: {ve}")
                return "No context/memory prompt..."
        except SlackApiError as e:
            logging.error(f"Error retrieving message history: {e}")
            return []

    @staticmethod
    def is_scheduled_message(event: Dict) -> bool:
        """Check if the event is a scheduled message."""
        # Check if it's a message from the bot and starts with our prefix
        is_bot_message = event.get("bot_id") is not None
        has_prefix = event.get("text", "").startswith("Running scheduled message:")
        return is_bot_message and has_prefix

    @staticmethod
    def is_dm(event: Dict) -> bool:
        """Check if the event is a direct message."""
        # Only check for direct messages, scheduled messages are handled separately
        return event.get("channel_type") == "im" and event.get("subtype") is None

    @staticmethod
    def log_error(error: str, body: Dict) -> None:
        """Log errors and request body."""

        logging.error(f"Slack API error: {error}")
        logging.info(f"Request body: {body}")

    def process_message(self, client: WebClient, event: Dict) -> None:
        """Handle messages and generate responses using AI."""
        logging.info(f"Received message event: {event}")

        # Extract event details for deduplication
        event_id = (
            event.get("client_msg_id") or event.get("event_ts") or event.get("ts")
        )
        event_ts = event.get("event_ts") or event.get("ts")

        # For scheduled messages, use the event's channel_id and text directly
        if event.get("is_scheduled"):
            logging.info("Processing scheduled message")
            channel = event.get("channel_id")  # Use channel_id from event
            user_text = event.get("text", "").strip()
            user_id = "SCHEDULED_MESSAGE"
            # For scheduled messages, create a unique event_id
            event_id = f"scheduled_{event.get('channel_id')}_{event.get('ts', '')}"
            logging.info(
                f"Processing scheduled message - Channel: {channel}, Text: {user_text}"
            )
        else:
            user_id = event.get("user")
            user_text = event.get("text", "").strip()
            channel = event.get("channel")
            logging.info(
                f"Initial message details - user_id: {user_id}, text: {user_text}, channel: {channel}"
            )

        # Skip deduplication for scheduled messages or if no event_id
        if not event.get("is_scheduled") and event_id:
            # Check for duplicate messages using database
            with SessionLocal() as db:
                dedup_repo = MessageDeduplicationRepository(db)

                # Try to mark this message as processing
                if not dedup_repo.mark_message_processing(
                    event_id=event_id,
                    event_ts=event_ts,
                    channel_id=channel,
                    user_id=user_id,
                    message_text=user_text,
                ):
                    logging.info(
                        f"Message {event_id} already being processed, skipping duplicate"
                    )
                    return

        # Get user preferences
        with SessionLocal() as db:
            repo = UserPreferencesRepository(db)
            preferences = repo.get_preferences(user_id)

        try:
            placeholder = client.chat_postMessage(channel=channel, text="Thinking...")
            ts = placeholder.get("ts")
        except SlackApiError as e:
            logging.error(f"Error sending placeholder message: {e}")
            logging.error(f"The server responded with: {e.response}")
            # Mark as failed if we have an event_id
            if not event.get("is_scheduled") and event_id:
                with SessionLocal() as db:
                    dedup_repo = MessageDeduplicationRepository(db)
                    dedup_repo.mark_message_failed(event_id)
            return

        context = (
            self.get_message_history(client, user_id, channel)
            if not event.get(
                "is_scheduled"
            )  # Don't get message history for scheduled messages
            else [
                {
                    "role": "system",
                    "content": SlackPromptTemplate.no_memory_prompt.template,
                }
            ]
        )

        try:
            graph = Graph()
            class_agent, ai_response, ai_summary, sql_query, slack_blocks = (
                graph.execute_agent(message=user_text, memory=str(context))
            )
            logging.info(f"Class Agent: {class_agent}")
            logging.info(f"Final AI Response: {ai_response}")
            logging.info(f"Final AI Summary: {ai_summary}")
            logging.info(f"Final SQL Query: {sql_query}")
            logging.info(f"Slack Blocks: {slack_blocks}")
        except Exception as e:
            logging.error(f"Error executing AI agent: {e}")
            # Mark as failed if we have an event_id
            if not event.get("is_scheduled") and event_id:
                with SessionLocal() as db:
                    dedup_repo = MessageDeduplicationRepository(db)
                    dedup_repo.mark_message_failed(event_id)
            return

        try:
            if class_agent == "formatter_file":
                if slack_blocks:
                    # Get the base URL based on environment
                    base_url = "https://distillery-lab.fyi/dev"

                    # Create a new list for filtered blocks, starting with the summary
                    filtered_blocks = []

                    # Add summary block first (it's always shown)
                    for block in slack_blocks:
                        if (
                            block.get("type") == "section"
                            and block.get("text", {}).get("text", "")
                            and not block.get("text", {})
                            .get("text", "")
                            .startswith("```")
                        ):
                            filtered_blocks.append(block)
                            break

                    # Only show table blocks if the preference is enabled
                    if preferences.show_table_results:
                        # Add all table blocks from the formatter agent
                        for block in slack_blocks:
                            if (
                                block.get("type") == "section"
                                and block.get("text", {})
                                .get("text", "")
                                .startswith("```")
                                and not block.get("text", {})
                                .get("text", "")
                                .startswith("```sql")
                            ):
                                filtered_blocks.append(block)

                    client.chat_update(
                        channel=channel, ts=ts, text=ai_summary, blocks=filtered_blocks
                    )
                else:
                    # Create blocks with just the summary
                    filtered_blocks = [
                        {
                            "type": "section",
                            "text": {"type": "mrkdwn", "text": ai_summary},
                        }
                    ]
                    client.chat_update(
                        channel=channel, ts=ts, text=ai_summary, blocks=filtered_blocks
                    )

                # Get the current blocks from the message
                base_url = "https://distillery-lab.fyi/dev"
                result = client.conversations_history(
                    channel=channel, latest=ts, limit=1, inclusive=True
                )
                current_blocks = (
                    result["messages"][0].get("blocks", [])
                    if result["messages"]
                    else []
                )

                # Add the view table button to the blocks if enabled
                if preferences.show_detailed_results:
                    current_blocks.append(
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "View Detailed Results",
                                        "emoji": True,
                                    },
                                    "action_id": "view_detailed_results",
                                    "url": f"{base_url}/table?query_id={ai_response.get('query_id', '')}",
                                    "style": "primary",
                                }
                            ],
                        }
                    )

                # Add feedback buttons if enabled
                if preferences.show_feedback:
                    current_blocks.append(
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "👍",
                                        "emoji": True,
                                    },
                                    "action_id": "feedback_thumbs_up",
                                    "value": "thumbs_up",
                                },
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "👎",
                                        "emoji": True,
                                    },
                                    "action_id": "feedback_thumbs_down",
                                    "value": "thumbs_down",
                                },
                            ],
                        }
                    )

                # Add the SQL query toggle button if enabled
                if preferences.show_sql_query:
                    current_blocks.append(
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "▼ Show Executed Query",
                                        "emoji": True,
                                    },
                                    "action_id": "toggle_sql_details",
                                    "value": f"show:{sql_query}",
                                }
                            ],
                        }
                    )

                # Update the message with all blocks
                client.chat_update(
                    channel=channel, ts=ts, text=ai_summary, blocks=current_blocks
                )

            elif class_agent == "default_agent_answer":
                # For default agent, we just need to show the response and feedback buttons if enabled
                blocks = [
                    {"type": "section", "text": {"type": "mrkdwn", "text": ai_response}}
                ]

                # Add feedback buttons if enabled
                if preferences.show_feedback:
                    blocks.append(
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "👍",
                                        "emoji": True,
                                    },
                                    "action_id": "feedback_thumbs_up",
                                    "value": "thumbs_up",
                                },
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "👎",
                                        "emoji": True,
                                    },
                                    "action_id": "feedback_thumbs_down",
                                    "value": "thumbs_down",
                                },
                            ],
                        }
                    )
                client.chat_update(
                    channel=channel, ts=ts, text=ai_response, blocks=blocks
                )

            # Mark message as completed if we have an event_id
            if not event.get("is_scheduled") and event_id:
                with SessionLocal() as db:
                    dedup_repo = MessageDeduplicationRepository(db)
                    dedup_repo.mark_message_completed(event_id)

        except SlackApiError as e:
            logging.error(f"Error updating message: {e}")
            # Mark as failed if we have an event_id
            if not event.get("is_scheduled") and event_id:
                with SessionLocal() as db:
                    dedup_repo = MessageDeduplicationRepository(db)
                    dedup_repo.mark_message_failed(event_id)

    def handle_toggle_feedback(self, body: Dict, client: WebClient) -> None:
        """Handle the toggle feedback buttons action."""
        user_id = body["user"]["id"]
        selected_options = body["actions"][0].get("selected_options", [])

        with SessionLocal() as db:
            repo = UserPreferencesRepository(db)
            repo.update_preferences(
                user_id=user_id, show_feedback=len(selected_options) > 0
            )
            logging.info(f"Updated feedback preference for user {user_id}")

    def handle_toggle_detailed_results(self, body: Dict, client: WebClient) -> None:
        """Handle the toggle detailed results button action."""
        user_id = body["user"]["id"]
        selected_options = body["actions"][0].get("selected_options", [])

        with SessionLocal() as db:
            repo = UserPreferencesRepository(db)
            repo.update_preferences(
                user_id=user_id, show_detailed_results=len(selected_options) > 0
            )
            logging.info(f"Updated detailed results preference for user {user_id}")

    def handle_toggle_sql_query(self, body: Dict, client: WebClient) -> None:
        """Handle the toggle SQL query button action."""
        user_id = body["user"]["id"]
        selected_options = body["actions"][0].get("selected_options", [])

        with SessionLocal() as db:
            repo = UserPreferencesRepository(db)
            repo.update_preferences(
                user_id=user_id, show_sql_query=len(selected_options) > 0
            )
            logging.info(f"Updated SQL query preference for user {user_id}")

    def handle_toggle_table_results(self, body: Dict, client: WebClient) -> None:
        """Handle the toggle table results button action."""
        user_id = body["user"]["id"]
        selected_options = body["actions"][0].get("selected_options", [])

        with SessionLocal() as db:
            repo = UserPreferencesRepository(db)
            repo.update_preferences(
                user_id=user_id, show_table_results=len(selected_options) > 0
            )
            logging.info(f"Updated table results preference for user {user_id}")

    def view_detailed_results(self, body: Dict, client: WebClient) -> None:
        """Handle the view detailed results button action."""
        return {"ok": True}

    def handle_feedback(self, body: Dict, client: WebClient) -> None:
        """Handle feedback actions (thumbs up/down)."""
        try:
            action_id = body["actions"][0]["action_id"]
            reaction = action_id == "feedback_thumbs_up"
            channel_id = body["channel"]["id"]
            message_ts = body["message"]["ts"]
            user_id = body["user"]["id"]

            # Get the user's original question from conversation history
            try:
                conversation_history = client.conversations_history(
                    channel=channel_id,
                    latest=message_ts,
                    limit=10,
                    inclusive=True,
                ).get("messages", [])

                # Find the user's question by looking at messages before the bot's response
                user_question = None
                for msg in conversation_history:
                    # Skip the bot's response message
                    if msg.get("ts") == message_ts:
                        continue

                    # Look for a message from the user
                    if (
                        msg.get("user") == user_id
                        and msg.get("subtype") is None
                        and msg.get("text")
                    ):
                        user_question = msg.get("text")
                        break

            except Exception as e:
                logging.error(f"Error getting user question: {e}")
                user_question = None

            # Extract query_id from the View Detailed Results button if present
            s3_query_id = None
            try:
                message_blocks = body["message"]["blocks"]
                for block in message_blocks:
                    if block.get("type") == "actions":
                        for element in block.get("elements", []):
                            if element.get("text", {}).get(
                                "text"
                            ) == "View Detailed Results" and element.get("url"):
                                url = element.get("url")
                                if "query_id=" in url:
                                    s3_query_id = url.split("query_id=")[-1]
                                break
            except Exception as e:
                logging.error(f"Error extracting s3_query_id: {e}")

            # Create feedback object
            with SessionLocal() as db:
                feedback_data = FeedbackCreate(
                    message_ts=message_ts,
                    channel_id=channel_id,
                    user_id=user_id,
                    chatbot_response=body["message"]["text"],
                    user_question=user_question,
                    s3_query_id=s3_query_id,
                    reaction=reaction,
                )
                feedback_crud.create_feedback(db, feedback_data)

            # Get the original message blocks
            original_blocks = body["message"]["blocks"]

            # Filter out the feedback buttons block
            blocks = [
                block
                for block in original_blocks
                if not (
                    block.get("type") == "actions"
                    and any(
                        element.get("action_id")
                        in ["feedback_thumbs_up", "feedback_thumbs_down"]
                        for element in block.get("elements", [])
                    )
                )
            ]

            # Add a thank you message based on the reaction
            thank_you_text = (
                "✨ Thank you for your positive feedback! ✨"
                if reaction
                else "Thanks for your feedback. We'll work on improving!"
            )
            blocks.append(
                {
                    "type": "context",
                    "elements": [{"type": "mrkdwn", "text": thank_you_text}],
                }
            )

            # Update the message in Slack
            client.chat_update(
                channel=channel_id,
                ts=message_ts,
                blocks=blocks,
                text=body["message"]["text"],
            )

        except Exception as e:
            logging.error(f"Error handling feedback: {e}")
            raise

    def handle_schedule_msg(self, body: Dict, client: WebClient) -> None:
        """Handle the /schedule_msg slash command."""
        import time

        try:
            # Immediately acknowledge the command to prevent timeout
            ack = body.get("ack")
            if ack:
                ack()

            logging.info(f"Received schedule_msg command: {body}")

            command_text = body.get("text", "").strip()
            channel_id = body.get("channel_id")
            user_id = body.get("user_id")
            response_url = body.get(
                "response_url"
            )  # Get the response_url for delayed responses

            if not command_text:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="Please provide a message and schedule in one of these formats:\n"
                    + "1. One-time: 'Your message | YYYY-MM-DD HH:MM'\n"
                    + "2. Recurring: 'Your message | every [day] at [time]'\n\n"
                    + "Examples:\n"
                    + "- 'Send report | 2024-05-20 15:30'\n"
                    + "- 'Daily standup | every day at 9:30am'\n"
                    + "- 'Weekly review | every friday at 4pm'",
                )
                return

            parts = command_text.split("|", 1)
            if len(parts) != 2:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="Please separate your message and schedule with '|'. Examples:\n"
                    + "- 'Send report | 2024-05-20 15:30'\n"
                    + "- 'Daily standup | every day at 9:30am'\n"
                    + "- 'Weekly review | every friday at 4pm'",
                )
                return

            message = parts[0].strip()
            schedule_time_str = parts[1].strip()

            # First try to parse as a recurring schedule if it contains "every"
            if "every" in schedule_time_str.lower():
                eventbridge_service = EventBridgeService()
                cron_expression = eventbridge_service.parse_cron(schedule_time_str)

                if cron_expression:
                    # Handle recurring schedule
                    try:
                        with SessionLocal() as db:
                            repo = RecurringScheduleRepository(db)

                            # Create the schedule in the database
                            schedule_create = RecurringScheduleCreate(
                                user_id=user_id,
                                channel_id=channel_id,
                                message=message,
                                cron_expression=cron_expression,
                                timezone="UTC",  # You might want to get this from user preferences
                                is_active=True,
                            )

                            db_schedule = repo.create(schedule_create)

                            # Create the EventBridge rule
                            rule_arn = eventbridge_service.create_rule(db_schedule)

                            if rule_arn:
                                # Update the schedule with the rule ARN
                                repo.update_rule_arn(db_schedule.id, rule_arn)

                                # Use response_url for delayed response if available
                                if response_url:
                                    import requests

                                    requests.post(
                                        response_url,
                                        json={
                                            "response_type": "ephemeral",
                                            "text": f"✅ Recurring schedule created successfully!\n\n*Schedule:* {schedule_time_str}\n*Message:* {message}",
                                            "blocks": [
                                                {
                                                    "type": "section",
                                                    "text": {
                                                        "type": "mrkdwn",
                                                        "text": f"✅ *Recurring schedule created successfully!*\n\n*Schedule:* {schedule_time_str}\n*Message:* {message}",
                                                    },
                                                }
                                            ],
                                        },
                                    )
                                else:
                                    client.chat_postEphemeral(
                                        channel=channel_id,
                                        user=user_id,
                                        text=f"✅ Recurring schedule created successfully!\n\n*Schedule:* {schedule_time_str}\n*Message:* {message}",
                                        blocks=[
                                            {
                                                "type": "section",
                                                "text": {
                                                    "type": "mrkdwn",
                                                    "text": f"✅ *Recurring schedule created successfully!*\n\n*Schedule:* {schedule_time_str}\n*Message:* {message}",
                                                },
                                            }
                                        ],
                                    )
                                return
                            else:
                                error_msg = "Failed to create EventBridge rule. Please try again."
                                if response_url:
                                    requests.post(
                                        response_url,
                                        json={
                                            "response_type": "ephemeral",
                                            "text": error_msg,
                                        },
                                    )
                                else:
                                    client.chat_postEphemeral(
                                        channel=channel_id,
                                        user=user_id,
                                        text=error_msg,
                                    )
                                return

                    except Exception as e:
                        logging.error(f"Error creating recurring schedule: {e}")
                        error_msg = f"Error creating recurring schedule: {str(e)}"
                        if response_url:
                            requests.post(
                                response_url,
                                json={
                                    "response_type": "ephemeral",
                                    "text": error_msg,
                                },
                            )
                        else:
                            client.chat_postEphemeral(
                                channel=channel_id,
                                user=user_id,
                                text=error_msg,
                            )
                        return
                else:
                    # If parse_cron failed, show recurring schedule format error
                    client.chat_postEphemeral(
                        channel=channel_id,
                        user=user_id,
                        text="Invalid recurring schedule format. Please use format:\n"
                        + "'every [day] at [time]'\n\n"
                        + "Examples:\n"
                        + "- 'every day at 9:30am'\n"
                        + "- 'every monday at 2pm'\n"
                        + "- 'every friday at 15:45'",
                    )
                    return

            # If not a recurring schedule (no "every" keyword), handle as one-time schedule
            try:
                scheduled_timestamp = self._convert_to_timestamp(schedule_time_str)
                current_timestamp = int(time.time())

                # Validate future timestamp
                if scheduled_timestamp < current_timestamp + 30:
                    logging.info(
                        f"scheduled_timestamp: {scheduled_timestamp}, current_timestamp: {current_timestamp}"
                    )
                    client.chat_postEphemeral(
                        channel=channel_id,
                        user=user_id,
                        text="The scheduled time must be at least 30 seconds in the future.",
                    )
                    return

                # Handle one-time schedule using EventBridge (same as recurring)
                try:
                    eventbridge_service = EventBridgeService()

                    with SessionLocal() as db:
                        repo = RecurringScheduleRepository(db)

                        # Convert timestamp to ISO format for storage
                        from datetime import datetime, timezone

                        dt = datetime.fromtimestamp(
                            scheduled_timestamp, tz=timezone.utc
                        )
                        iso_time = dt.strftime("%Y-%m-%dT%H:%M:%S")

                        # Create the schedule in the database
                        schedule_create = RecurringScheduleCreate(
                            user_id=user_id,
                            channel_id=channel_id,
                            message=message,
                            cron_expression=iso_time,  # Store ISO timestamp instead of cron
                            timezone="UTC",
                            schedule_type="one_time",  # Mark as one-time schedule
                            is_active=True,
                        )

                        db_schedule = repo.create(schedule_create)

                        # Create the EventBridge rule using the new one-time method
                        rule_arn = eventbridge_service.create_one_time_rule(
                            schedule_id=db_schedule.id,
                            channel_id=channel_id,
                            user_id=user_id,
                            message=message,
                            timestamp=scheduled_timestamp,
                        )

                        if rule_arn:
                            # Update the schedule with the rule ARN
                            repo.update_rule_arn(db_schedule.id, rule_arn)

                            # Use response_url for delayed response if available
                            if response_url:
                                import requests

                                requests.post(
                                    response_url,
                                    json={
                                        "response_type": "ephemeral",
                                        "text": f"✅ One-time schedule created successfully!\n\n*Time:* {schedule_time_str}\n*Message:* {message}",
                                        "blocks": [
                                            {
                                                "type": "section",
                                                "text": {
                                                    "type": "mrkdwn",
                                                    "text": f"✅ *One-time schedule created successfully!*\n\n*Time:* {schedule_time_str}\n*Message:* {message}",
                                                },
                                            }
                                        ],
                                    },
                                )
                            else:
                                client.chat_postEphemeral(
                                    channel=channel_id,
                                    user=user_id,
                                    text=f"✅ One-time schedule created successfully!\n\n*Time:* {schedule_time_str}\n*Message:* {message}",
                                    blocks=[
                                        {
                                            "type": "section",
                                            "text": {
                                                "type": "mrkdwn",
                                                "text": f"✅ *One-time schedule created successfully!*\n\n*Time:* {schedule_time_str}\n*Message:* {message}",
                                            },
                                        }
                                    ],
                                )
                            return
                        else:
                            error_msg = (
                                "Failed to create EventBridge rule. Please try again."
                            )
                            if response_url:
                                import requests

                                requests.post(
                                    response_url,
                                    json={
                                        "response_type": "ephemeral",
                                        "text": error_msg,
                                    },
                                )
                            else:
                                client.chat_postEphemeral(
                                    channel=channel_id,
                                    user=user_id,
                                    text=error_msg,
                                )
                            return

                except Exception as e:
                    logging.error(f"Error creating one-time schedule: {e}")
                    error_msg = f"Error creating one-time schedule: {str(e)}"
                    if response_url:
                        import requests

                        requests.post(
                            response_url,
                            json={
                                "response_type": "ephemeral",
                                "text": error_msg,
                            },
                        )
                    else:
                        client.chat_postEphemeral(
                            channel=channel_id,
                            user=user_id,
                            text=error_msg,
                        )
                    return

            except ValueError as e:
                logging.warning(f"Datetime parsing failed: {e}")
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="Invalid schedule format. Please use one of these formats:\n"
                    + "1. One-time: 'Your message | YYYY-MM-DD HH:MM'\n"
                    + "2. Recurring: 'Your message | every [day] at [time]'\n\n"
                    + "Examples:\n"
                    + "- 'Send report | 2024-05-20 15:30'\n"
                    + "- 'Daily standup | every day at 9:30am'\n"
                    + "- 'Weekly review | every friday at 4pm'",
                )

            except SlackApiError as e:
                logging.error(f"Slack API error: {e.response['error']}")
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text=f"Slack API error: {e.response['error']}",
                )

        except Exception:
            logging.exception("Unexpected error in handle_schedule_msg")
            raise

    def _convert_to_timestamp(self, datetime_str: str) -> int:
        """Convert a datetime string in local system time to a UTC Unix timestamp.

        Args:
            datetime_str: 'YYYY-MM-DD HH:MM'

        Returns:
            int: Unix timestamp in UTC
        """
        import datetime

        try:
            # Parse naive datetime (user assumes it's local)
            naive_dt = datetime.datetime.strptime(datetime_str, "%Y-%m-%d %H:%M")

            # Attach local timezone from the system
            local_dt = naive_dt.astimezone()  # automatic local timezone
            utc_dt = local_dt.astimezone(datetime.timezone.utc)

            return int(utc_dt.timestamp())

        except ValueError as e:
            raise ValueError(
                "Please use the format 'YYYY-MM-DD HH:MM'. Error: " + str(e)
            )

    def handle_list_schedules(self, body: Dict, client: WebClient) -> None:
        """Handle the /list_schedules slash command."""
        try:
            logging.info(f"Received list_schedules command: {body}")

            user_id = body.get("user_id")
            channel_id = body.get("channel_id")

            # Get user's scheduled messages from database
            with SessionLocal() as db:
                repo = RecurringScheduleRepository(db)
                user_schedules = repo.get_by_user(user_id)
                active_schedules = [s for s in user_schedules if s.is_active]

            if not active_schedules:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="📅 You don't have any active scheduled messages.",
                    blocks=[
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": "📅 *No Active Scheduled Messages*\n\nYou don't have any active scheduled messages. Use `/schedule_msg` to create one!",
                            },
                        }
                    ],
                )
                return

            # Format the schedules for display
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"📅 Your Active Scheduled Messages ({len(active_schedules)})",
                        "emoji": True,
                    },
                }
            ]

            for i, schedule in enumerate(active_schedules, 1):
                # Format the schedule display
                schedule_text = self._format_schedule_display(schedule)

                blocks.extend(
                    [
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"*{i}.* {schedule_text}",
                            },
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "🗑️ Delete",
                                        "emoji": True,
                                    },
                                    "action_id": "delete_schedule_button",
                                    "value": str(schedule.id),
                                    "style": "danger",
                                    "confirm": {
                                        "title": {
                                            "type": "plain_text",
                                            "text": "Delete Schedule",
                                        },
                                        "text": {
                                            "type": "mrkdwn",
                                            "text": f"Are you sure you want to delete this scheduled message?\n\n*Message:* {schedule.message[:100]}{'...' if len(schedule.message) > 100 else ''}",
                                        },
                                        "confirm": {
                                            "type": "plain_text",
                                            "text": "Delete",
                                        },
                                        "deny": {
                                            "type": "plain_text",
                                            "text": "Cancel",
                                        },
                                    },
                                }
                            ],
                        },
                        {"type": "divider"},
                    ]
                )

            # Remove the last divider
            if blocks and blocks[-1]["type"] == "divider":
                blocks.pop()

            # Add footer with instructions
            blocks.append(
                {
                    "type": "context",
                    "elements": [
                        {
                            "type": "mrkdwn",
                            "text": "💡 Use `/delete_schedule <schedule_id>` to delete a specific schedule, or click the delete buttons above.",
                        }
                    ],
                }
            )

            client.chat_postEphemeral(
                channel=channel_id,
                user=user_id,
                text=f"You have {len(active_schedules)} active scheduled messages.",
                blocks=blocks,
            )

        except Exception as e:
            logging.error(f"Error in handle_list_schedules: {e}")
            client.chat_postEphemeral(
                channel=channel_id,
                user=user_id,
                text="❌ An error occurred while retrieving your scheduled messages. Please try again.",
            )

    def handle_delete_schedule(self, body: Dict, client: WebClient) -> None:
        """Handle the /delete_schedule slash command."""
        try:
            logging.info(f"Received delete_schedule command: {body}")

            command_text = body.get("text", "").strip()
            user_id = body.get("user_id")
            channel_id = body.get("channel_id")

            if not command_text:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="Please provide a schedule ID to delete.\n\nUsage: `/delete_schedule <schedule_id>`\n\nUse `/list_schedules` to see your active schedules and their IDs.",
                )
                return

            # Try to parse the schedule ID
            try:
                schedule_id = UUID(command_text)
            except ValueError:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="❌ Invalid schedule ID format. Please provide a valid UUID.\n\nUse `/list_schedules` to see your active schedules and their IDs.",
                )
                return

            # Delete the schedule
            success, message = self._delete_schedule(schedule_id, user_id)

            if success:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="✅ Schedule deleted successfully!",
                    blocks=[
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"✅ *Schedule deleted successfully!*\n\n{message}",
                            },
                        }
                    ],
                )
            else:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text=f"❌ {message}",
                )

        except Exception as e:
            logging.error(f"Error in handle_delete_schedule: {e}")
            client.chat_postEphemeral(
                channel=channel_id,
                user=user_id,
                text="❌ An error occurred while deleting the schedule. Please try again.",
            )

    def _format_schedule_display(self, schedule) -> str:
        """Format a schedule for display in the list."""
        from croniter import croniter
        from datetime import datetime
        import pytz

        # Format the message (truncate if too long)
        message_preview = (
            schedule.message[:80] + "..."
            if len(schedule.message) > 80
            else schedule.message
        )

        # Handle different schedule types
        if schedule.schedule_type == "one_time":
            # For one-time schedules, parse the ISO timestamp
            try:
                scheduled_time = datetime.fromisoformat(schedule.cron_expression)
                next_run_str = scheduled_time.strftime("%Y-%m-%d %H:%M UTC")
                schedule_desc = f"One-time at {next_run_str}"
            except Exception:
                next_run_str = "Unable to calculate"
                schedule_desc = f"One-time: {schedule.cron_expression}"
        else:
            # For recurring schedules, parse cron expression to show next run time
            try:
                now = datetime.now(pytz.UTC)
                cron = croniter(schedule.cron_expression, now)
                next_run = cron.get_next(datetime)
                next_run_str = next_run.strftime("%Y-%m-%d %H:%M UTC")
                schedule_desc = f"Recurring: {schedule.cron_expression}"
            except Exception:
                next_run_str = "Unable to calculate"
                schedule_desc = f"Recurring: {schedule.cron_expression}"

        # Format the display
        return f"*Message:* {message_preview}\n*Schedule:* {schedule_desc}\n*Next Run:* {next_run_str}\n*ID:* `{schedule.id}`"

    def _delete_schedule(self, schedule_id, user_id: str) -> tuple[bool, str]:
        """Delete a schedule and clean up EventBridge rule."""
        try:
            with SessionLocal() as db:
                repo = RecurringScheduleRepository(db)
                return repo.delete_schedule(schedule_id, user_id)

        except Exception as e:
            logging.error(f"Error deleting schedule {schedule_id}: {e}")
            return False, f"An error occurred while deleting the schedule: {str(e)}"

    def handle_delete_schedule_button(self, body: Dict, client: WebClient) -> None:
        """Handle the delete schedule button action."""
        try:
            schedule_id_str = body["actions"][0]["value"]
            user_id = body["user"]["id"]
            channel_id = body["channel"]["id"]

            # Parse the schedule ID
            try:
                schedule_id = UUID(schedule_id_str)
            except ValueError:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="❌ Invalid schedule ID format.",
                )
                return

            # Delete the schedule
            success, message = self._delete_schedule(schedule_id, user_id)

            if success:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="✅ Schedule deleted successfully!",
                    blocks=[
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"✅ *Schedule deleted successfully!*\n\n{message}",
                            },
                        }
                    ],
                )
            else:
                client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text=f"❌ {message}",
                )

        except Exception as e:
            logging.error(f"Error handling delete schedule button: {e}")
            client.chat_postEphemeral(
                channel=channel_id,
                user=user_id,
                text="❌ An error occurred while deleting the schedule. Please try again.",
            )
