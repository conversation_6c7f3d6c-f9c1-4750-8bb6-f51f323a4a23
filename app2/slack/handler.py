import json
import logging
import traceback
import sys

from slack_bolt.adapter.aws_lambda import (
    SlackRequestHandler as AWSLambdaSlackRequestHandler,
)
from slack_bolt.adapter.fastapi import <PERSON>lackRequestHandler as FastAPISlackRequestHandler

from app2.slack.slack_app import Slack<PERSON>otApp
from app2.slack.verification import handle_slack_verification
from app2.utils import is_running_in_lambda

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout)  # Always use stdout for Lambda
    ],
)

# Create logger for this module
logger = logging.getLogger(__name__)

# Remove any existing handlers to avoid duplicate logs
for handler in logger.handlers[:]:
    logger.removeHandler(handler)

# Add stdout handler
handler = logging.StreamHandler(sys.stdout)
handler.setFormatter(logging.Formatter("%(levelname)s:---> %(message)s"))
logger.addHandler(handler)

# Set propagate to False to avoid duplicate logs
logger.propagate = False

# Initialize Slack app
slack_app = SlackBotApp().app

# Determine which handler to use based on environment
is_lambda = is_running_in_lambda()
if is_lambda:
    handler = AWSLambdaSlackRequestHandler(slack_app)
    logger.info("Using AWS Lambda Slack Request Handler")
else:
    handler = FastAPISlackRequestHandler(slack_app)
    logger.info("Using FastAPI Slack Request Handler")


async def process_slack_events(request, context=None):
    """Process incoming Slack events."""
    try:
        # Log the request
        if hasattr(request, "body"):
            logger.info(f"Request body: {request.body}")
        if hasattr(request, "headers"):
            logger.info(f"Request headers: {dict(request.headers)}")

        # First check if this is a verification challenge
        verification_response = await handle_slack_verification(request)
        if verification_response is not None:
            logger.info("Handled verification challenge")
            return verification_response

        # If not a verification challenge, process normally
        if is_lambda:
            logger.info("Processing with Lambda handler")
            try:
                response = await handler.handle(request, context)
                logger.info("Lambda handler response received")
                return response
            except Exception as e:
                logger.error(f"Error in Lambda handler: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
        else:
            logger.info("Processing with FastAPI handler")
            try:
                response = await handler.handle(request)
                logger.info("FastAPI handler response received")
                return response
            except Exception as e:
                logger.error(f"Error in FastAPI handler: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
    except Exception as e:
        logger.error(f"Error processing Slack event: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}


async def process_slack_commands(request, context=None):
    """Process incoming Slack slash commands."""
    try:
        # Log the request
        if hasattr(request, "body"):
            logger.info(f"Slash command request body: {request.body}")
        if hasattr(request, "headers"):
            logger.info(f"Slash command request headers: {dict(request.headers)}")

        # Process the command
        if is_lambda:
            logger.info("Processing slash command with Lambda handler")
            try:
                response = await handler.handle(request, context)
                logger.info("Lambda handler response for slash command received")
                return response
            except Exception as e:
                logger.error(f"Error in Lambda handler for slash command: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
        else:
            logger.info("Processing slash command with FastAPI handler")
            try:
                response = await handler.handle(request)
                logger.info("FastAPI handler response for slash command received")
                return response
            except Exception as e:
                logger.error(f"Error in FastAPI handler for slash command: {str(e)}")
                logger.error(f"Traceback: {traceback.format_exc()}")
                return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
    except Exception as e:
        logger.error(f"Error processing Slack slash command: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return {"statusCode": 500, "body": json.dumps({"error": str(e)})}
