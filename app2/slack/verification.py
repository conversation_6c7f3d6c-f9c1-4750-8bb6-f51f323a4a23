from fastapi import Request
from fastapi.responses import JSONResponse


async def handle_slack_verification(request: Request) -> JSONResponse:
    """Handle Slack URL verification challenge."""
    body = await request.json()

    # If this is a URL verification challenge
    if body.get("type") == "url_verification":
        # Return the challenge value
        return JSONResponse(content={"challenge": body.get("challenge")})

    return None
