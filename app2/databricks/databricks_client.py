import json
import logging
import time
from typing import Optional, Tuple

import requests

from app2.config import Settings

# Create a Settings instance
app_config = Settings()


class DatabricksAgent:
    """
    A class to interact with Databricks' Genie API for creating conversations, generating SQL queries,
    and retrieving results in a structured manner.
    """

    HEADERS = {
        "Authorization": f"Bearer {app_config.access_token}",
        "Content-Type": "application/json",
    }

    def __init__(self, user_message: str) -> None:
        """
        Initializes the DatabricksAgent with the user's query message.

        :param user_message: The input message from the user to be processed by <PERSON>ie.
        """
        self.user_message = user_message
        self.conversation_id: Optional[str] = None
        self.message_id: Optional[str] = None
        self.attachment_id: Optional[str] = None

    def log_step(func):
        """
        Decorator to log the execution of each step.
        """

        def wrapper(self, *args, **kwargs):
            logging.info(f"--> Executing: {func.__name__}")
            result = func(self, *args, **kwargs)
            logging.info(f"--> Completed: {func.__name__}")
            return result

        return wrapper

    @log_step
    def create_conversation(self) -> bool:
        """
        Creates a new conversation in Databricks Genie API.

        :return: True if conversation was successfully created, False otherwise.
        """
        url = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/start-conversation"
        payload = json.dumps({"content": self.user_message})

        # Log auth details (masked) and request info for debugging
        logging.info(f"Making request to Databricks: {url}")
        logging.info(
            f"With headers: Authorization: Bearer {app_config.access_token[:4]}...{app_config.access_token[-4:] if len(app_config.access_token) > 8 else '****'}"
        )
        logging.info(f"Space ID: {app_config.space_id}")

        response = requests.post(url, headers=self.HEADERS, data=payload)

        if response.status_code == 200:
            result = response.json()
            self.conversation_id = result["conversation_id"]
            self.message_id = result["message_id"]
            logging.info(
                f"Conversation created: {self.conversation_id}, Message ID: {self.message_id}"
            )
            return True
        else:
            logging.error(
                f"Error creating conversation: {response.status_code}, {response.text}"
            )
            # Log additional debug information
            logging.error(f"Request details - URL: {url}, Payload: {payload}")
            logging.error(f"Databricks host: {app_config.databricks_host}")
            return False

    @log_step
    def wait_for_completion(self) -> bool:
        """
        Waits until the conversation processing is completed.

        :return: True if processing completed successfully, False otherwise.
        """
        if not self.conversation_id or not self.message_id:
            logging.error("Missing conversation or message ID.")
            return False

        url = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/conversations/{self.conversation_id}/messages/{self.message_id}"
        max_retries = 50

        with requests.Session() as session:
            for attempt in range(max_retries):
                try:
                    response = session.get(url, headers=self.HEADERS, timeout=10)

                    if response.status_code == 200:
                        status = response.json().get("status")
                        logging.info(f"Attempt {attempt + 1}: status: '{status}'")

                        if status == "COMPLETED":
                            return True
                        elif status in ["FAILED", "ERROR"]:
                            return False
                    else:
                        logging.error(f"Error {response.status_code}: {response.text}")

                except requests.exceptions.RequestException as e:
                    logging.error(f"Error in request: {e}")

                time.sleep(5)

        logging.error("Max retries Error")
        return False

    @log_step
    def fetch_attachments_generated(self) -> Optional[Tuple[str, str]]:
        """
        Retrieves the SQL query and data description generated by Genie.

        :return: Tuple of (query_result, data_description) or None if failed.
        """
        if not self.conversation_id or not self.message_id:
            logging.error("Missing conversation or message ID.")
            return None

        url = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/conversations/{self.conversation_id}/messages/{self.message_id}"
        session = requests.Session()

        try:
            response = session.get(url, headers=self.HEADERS, timeout=10)
            response.raise_for_status()
            result = response.json()

            attachments = result.get("attachments", [])
            if not attachments:
                logging.warning("No attachments found in Genie response.")
                return None

            attachment = attachments[0]
            self.attachment_id = attachment.get("attachment_id")

            query_data = attachment.get("query", {})
            query_result = query_data.get("query")
            data_description = query_data.get("description")

            if not all([self.attachment_id, query_result, data_description]):
                logging.error("Missing one or more expected fields in the attachment.")
                return None

            return query_result, data_description

        except (requests.exceptions.RequestException, ValueError) as e:
            logging.error(f"Error fetching SQL query: {e}")
            return None

        finally:
            session.close()

    @log_step
    def fetch_query_results(self) -> Optional[dict]:
        """
        Retrieves the structured output data result.
        """
        if not self.conversation_id or not self.message_id or not self.attachment_id:
            logging.error("Missing required IDs.")
            return None

        url = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/conversations/{self.conversation_id}/messages/{self.message_id}/attachments/{self.attachment_id}/query-result"

        session = requests.Session()
        try:
            response = session.get(url, headers=self.HEADERS, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logging.error(f"Error fetching query results: {e}")
            return None
        finally:
            session.close()

    def execute(self) -> Tuple[str, str, dict]:
        """
        Executes the full pipeline of querying Databricks Genie and fetching results.

        :return: A tuple of (query_result, data_description, result_data)
                 If any errors occur, the appropriate error messages are returned
                 and an empty dict or special error dict for the result_data
        """
        if not self.create_conversation():
            logging.error("Failed to create a conversation with Databricks")
            return (
                "[Error] - No query",
                "Error: Failed to authenticate or create a conversation.",
                {
                    "error": "authentication_failed",
                    "error_message": "Failed to create a conversation with Databricks. Please check your authentication credentials.",
                },
            )

        if not self.wait_for_completion():
            logging.error("Query processing failed or timed out")
            return (
                "[Error] - No query",
                "Error: Query processing failed or timed out.",
                {
                    "error": "processing_failed",
                    "error_message": "The query processing failed or timed out in Databricks.",
                },
            )

        attachment_result = self.fetch_attachments_generated()
        if not attachment_result:
            logging.error("Failed to fetch query attachments")
            return (
                "[Error] - No query or data description",
                "Error: Failed to fetch query information.",
                {
                    "error": "no_attachments",
                    "error_message": "No query information was returned from Databricks.",
                },
            )
        query_result, data_description = attachment_result

        core_result = self.fetch_query_results()
        if core_result is None:
            logging.error("Failed to fetch query results")
            return (
                query_result,
                "Query executed, but no results were returned.",
                {
                    "statement_response": {
                        "status": {"state": "COMPLETED"},
                        "manifest": {"schema": {"columns": []}},
                        "result": {},
                    }
                },
            )

        return query_result, data_description, core_result
