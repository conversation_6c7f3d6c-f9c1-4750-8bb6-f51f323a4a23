from fastapi import APIRouter, Request
from app2.slack.handler import process_slack_events, process_slack_commands
from app2.slack.verification import handle_slack_verification

router = APIRouter()


@router.post("/slack/events")
async def slack_events(
    request: Request,
):
    """Handle Slack events and URL verification"""
    # Check if it's a verification request
    body = await request.json()
    if body.get("type") == "url_verification":
        return await handle_slack_verification(request)

    # If not verification, process the event
    return await process_slack_events(request)


@router.post("/slack/commands")
async def slack_commands(
    request: Request,
):
    """Handle Slack slash commands"""
    return await process_slack_commands(request)
