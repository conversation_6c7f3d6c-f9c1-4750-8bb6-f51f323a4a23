from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from app2.db.session import get_db
from app2.schemas.feedback import FeedbackCreate
from app2.schemas.slack import SlackInteractionPayload
from app2.crud import feedback as feedback_crud
from app2.config import Settings
from slack_sdk.web import WebClient
from app2.db.repositories import UserPreferencesRepository
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
import json
import logging
import os
from uuid import UUID

logger = logging.getLogger(__name__)
settings = Settings()
slack_client = WebClient(token=settings.slack_bot_token)

router = APIRouter(
    prefix="/slack",
    tags=["slack"],
    responses={404: {"description": "Not found"}},
)


@router.post(
    "/interactions",
    summary="Handle Slack interaction events",
    openapi_extra={
        "requestBody": {
            "content": {
                "application/x-www-form-urlencoded": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "payload": {
                                "type": "string",
                                "example": json.dumps(
                                    {
                                        "type": "block_actions",
                                        "actions": [
                                            {"action_id": "thumbs_up", "type": "button"}
                                        ],
                                        "message": {
                                            "ts": "1234567890.123456",
                                            "text": "This is the chatbot response that was rated",
                                        },
                                        "channel": {"id": "C1234567890"},
                                        "user": {"id": "U1234567890"},
                                    }
                                ),
                            }
                        },
                        "required": ["payload"],
                    }
                }
            }
        }
    },
)
async def handle_slack_interaction(
    request: Request,
    db: Session = Depends(get_db),
):
    """
    Handle Slack interaction events for feedback buttons and SQL toggle.

    This endpoint receives interaction payloads from Slack when users:
    1. Click the thumbs up/down buttons (stores feedback in the database)
    2. Click the SQL toggle button (shows/hides SQL query)
    3. Click the home tab toggle buttons (updates user preferences)

    The payload should be sent as form data with a "payload" field containing the JSON string.
    Do not include "payload=" in the JSON string itself.
    """
    try:
        # Log environment information
        logger.info(
            f"Running in Lambda: {bool(os.environ.get('AWS_LAMBDA_FUNCTION_NAME'))}"
        )

        # Get form data
        form_data = await request.form()
        payload = form_data.get("payload")

        if not payload:
            raise HTTPException(status_code=400, detail="No payload provided")

        logger.info(f"Received interaction payload: {payload}")

        # Handle URL-encoded payload
        if payload.startswith("payload="):
            payload = payload[8:]  # Remove "payload=" prefix
            logger.info(f"Removed 'payload=' prefix. New payload: {payload}")

        # Parse the form-encoded payload into our Pydantic model
        try:
            interaction_data = SlackInteractionPayload.model_validate_json(payload)
            logger.info(f"Successfully parsed interaction data: {interaction_data}")
        except Exception as parse_error:
            logger.error(f"Failed to parse payload as JSON: {str(parse_error)}")
            logger.error(f"Raw payload content: {payload}")
            raise HTTPException(
                status_code=400, detail=f"Invalid payload format: {str(parse_error)}"
            )

        # Get the action ID from the first action
        if not interaction_data.actions:
            logger.error("No actions found in the interaction data")
            raise HTTPException(status_code=400, detail="No actions found in payload")

        action = interaction_data.actions[0]
        action_id = action.action_id
        logger.info(f"Processing action_id: {action_id}")

        # Handle home tab toggle actions
        if action_id in [
            "toggle_feedback",
            "toggle_detailed_results",
            "toggle_table_results",
            "toggle_sql_query",
        ]:
            user_id = interaction_data.user.id
            selected_options = action.selected_options or []
            is_enabled = len(selected_options) > 0

            repo = UserPreferencesRepository(db)

            if action_id == "toggle_feedback":
                repo.update_preferences(user_id=user_id, show_feedback=is_enabled)
            elif action_id == "toggle_detailed_results":
                repo.update_preferences(
                    user_id=user_id, show_detailed_results=is_enabled
                )
            elif action_id == "toggle_sql_query":
                repo.update_preferences(user_id=user_id, show_sql_query=is_enabled)
            elif action_id == "toggle_table_results":
                repo.update_preferences(user_id=user_id, show_table_results=is_enabled)

            logger.info(
                f"Updated {action_id} preference for user {user_id} to {is_enabled}"
            )
            return {"ok": True}

        # Handle SQL toggle action
        elif action_id == "toggle_sql_details":
            action_value = action.value
            parts = action_value.split(":", 1)
            action = parts[0]
            sql_query = parts[1] if len(parts) > 1 else ""

            # Get current blocks from the message
            current_blocks = interaction_data.message.blocks

            # Filter out any existing SQL query blocks and toggle button
            filtered_blocks = [
                block
                for block in current_blocks
                if not (
                    (
                        block.get("type") == "section"
                        and block.get("text", {}).get("text", "").startswith("```sql")
                    )
                    or (
                        block.get("type") == "actions"
                        and block.get("elements", [])
                        and any(
                            element.get("action_id") == "toggle_sql_details"
                            for element in block.get("elements", [])
                        )
                    )
                )
            ]

            # Add new SQL blocks based on action
            if action == "show":
                filtered_blocks.extend(
                    [
                        {
                            "type": "section",
                            "text": {
                                "type": "mrkdwn",
                                "text": f"```sql\n\n{sql_query}\n```",
                            },
                        },
                        {
                            "type": "actions",
                            "elements": [
                                {
                                    "type": "button",
                                    "text": {
                                        "type": "plain_text",
                                        "text": "▲ Hide Executed Query",
                                        "emoji": True,
                                    },
                                    "action_id": "toggle_sql_details",
                                    "value": f"hide:{sql_query}",
                                }
                            ],
                        },
                    ]
                )
            else:  # hide
                filtered_blocks.append(
                    {
                        "type": "actions",
                        "elements": [
                            {
                                "type": "button",
                                "text": {
                                    "type": "plain_text",
                                    "text": "▼ Show Executed Query",
                                    "emoji": True,
                                },
                                "action_id": "toggle_sql_details",
                                "value": f"show:{sql_query}",
                            }
                        ],
                    }
                )

            try:
                slack_client.chat_update(
                    channel=interaction_data.channel.id,
                    ts=interaction_data.message.ts,
                    blocks=filtered_blocks,
                )
                return {"ok": True}
            except Exception as e:
                logger.error(f"Error updating SQL toggle message: {str(e)}")
                raise HTTPException(
                    status_code=500, detail="Error updating Slack message"
                )

        elif action_id == "view_detailed_results":
            return {"ok": True}

        # Handle feedback actions
        elif action_id in ["feedback_thumbs_up", "feedback_thumbs_down"]:
            logger.info(f"Processing feedback action: {action_id}")
            # Determine reaction value based on the action_id
            reaction = action_id == "feedback_thumbs_up"
            logger.info(f"Setting reaction value to: {reaction}")

            # Get the user's original question from conversation history
            try:
                logger.info(
                    f"Attempting to get conversation history for channel {interaction_data.channel.id} and message {interaction_data.message.ts}"
                )
                # Get conversation history including the bot's response and previous messages
                conversation_history = slack_client.conversations_history(
                    channel=interaction_data.channel.id,
                    latest=interaction_data.message.ts,
                    limit=10,  # Increased limit to ensure we find the user's question
                    inclusive=True,
                ).get("messages", [])
                logger.info(
                    f"Retrieved {len(conversation_history)} messages from conversation history"
                )

                # Find the user's question by looking at messages before the bot's response
                user_question = None
                for msg in conversation_history:
                    # Skip the bot's response message
                    if msg.get("ts") == interaction_data.message.ts:
                        logger.info("Skipping bot's response message")
                        continue

                    # Look for a message from the user
                    if (
                        msg.get("user") == interaction_data.user.id
                        and msg.get("subtype") is None
                        and msg.get("text")
                    ):
                        user_question = msg.get("text")
                        logger.info(f"Found user question: {user_question}")
                        break

                if not user_question:
                    logger.warning(
                        "Could not find user question in conversation history"
                    )

            except Exception as e:
                logger.error(f"Error getting user question: {e}")
                user_question = None

            # Extract query_id from the View Detailed Results button if present
            s3_query_id = None
            try:
                # Get blocks from the message
                message_blocks = interaction_data.message.blocks
                logger.info(f"Processing message blocks: {message_blocks}")

                # Look for the View Detailed Results button to extract query_id
                for block in message_blocks:
                    if block.get("type") == "actions":
                        for element in block.get("elements", []):
                            if element.get("text", {}).get(
                                "text"
                            ) == "View Detailed Results" and element.get("url"):
                                url = element.get("url")
                                # Extract query_id from URL
                                if "query_id=" in url:
                                    s3_query_id = url.split("query_id=")[-1]
                                    logger.info(f"Found s3_query_id: {s3_query_id}")
                                break

                if not s3_query_id:
                    logger.info(
                        "No s3_query_id found in message blocks - this might be a simple response without query results"
                    )

            except Exception as e:
                logger.error(f"Error extracting s3_query_id: {e}")
                s3_query_id = None

            # Create feedback object with user question and s3_query_id
            try:
                feedback_data = FeedbackCreate(
                    message_ts=interaction_data.message.ts,
                    channel_id=interaction_data.channel.id,
                    user_id=interaction_data.user.id,
                    chatbot_response=interaction_data.message.text,
                    user_question=user_question,
                    s3_query_id=s3_query_id,  # Add the s3_query_id
                    reaction=reaction,
                )
                logger.info(f"Created feedback data object: {feedback_data}")

                # Save to database
                feedback_crud.create_feedback(db, feedback_data)
                logger.info("Successfully saved feedback to database")
            except Exception as e:
                logger.error(f"Error creating or saving feedback: {e}")
                raise HTTPException(
                    status_code=500, detail=f"Error saving feedback: {str(e)}"
                )

            # Get the original message blocks
            original_blocks = interaction_data.message.blocks
            logger.info(f"Original message blocks: {original_blocks}")

            # Filter out the feedback buttons block by checking for the specific action IDs
            blocks = [
                block
                for block in original_blocks
                if not (
                    block.get("type") == "actions"
                    and any(
                        element.get("action_id")
                        in ["feedback_thumbs_up", "feedback_thumbs_down"]
                        for element in block.get("elements", [])
                    )
                )
            ]
            logger.info(f"Filtered blocks after removing feedback buttons: {blocks}")

            # Add a different thank you message based on the reaction
            thank_you_text = (
                "✨ Thank you for your positive feedback! ✨"
                if reaction
                else "Thanks for your feedback. We'll work on improving!"
            )
            blocks.append(
                {
                    "type": "context",
                    "elements": [{"type": "mrkdwn", "text": thank_you_text}],
                }
            )
            logger.info(f"Final blocks with thank you message: {blocks}")

            # Update the message in Slack
            try:
                logger.info(
                    f"Attempting to update Slack message in channel {interaction_data.channel.id} with ts {interaction_data.message.ts}"
                )
                slack_client.chat_update(
                    channel=interaction_data.channel.id,
                    ts=interaction_data.message.ts,
                    blocks=blocks,
                    text=interaction_data.message.text,  # Preserve the original text
                )
                logger.info("Successfully updated Slack message")
            except Exception as e:
                logger.error(f"Error updating Slack message: {str(e)}")
                raise HTTPException(
                    status_code=500, detail=f"Error updating Slack message: {str(e)}"
                )

            # Return an empty 200 response to acknowledge the interaction
            return {"ok": True}

        # Handle delete schedule button action
        elif action_id == "delete_schedule_button":
            logger.info("Processing delete schedule button action")

            try:
                schedule_id_str = action.value
                user_id = interaction_data.user.id
                channel_id = interaction_data.channel.id

                # Parse the schedule ID
                try:
                    schedule_id = UUID(schedule_id_str)
                except ValueError:
                    logger.error(f"Invalid schedule ID format: {schedule_id_str}")
                    # Send ephemeral error message
                    slack_client.chat_postEphemeral(
                        channel=channel_id,
                        user=user_id,
                        text="❌ Invalid schedule ID format.",
                    )
                    return {"ok": True}

                # Use the RecurringScheduleRepository's delete method
                repo = RecurringScheduleRepository(db)
                success, message = repo.delete_schedule(schedule_id, user_id)

                if success:
                    slack_client.chat_postEphemeral(
                        channel=channel_id,
                        user=user_id,
                        text="✅ Schedule deleted successfully!",
                        blocks=[
                            {
                                "type": "section",
                                "text": {
                                    "type": "mrkdwn",
                                    "text": f"✅ *Schedule deleted successfully!*\n\n{message}",
                                },
                            }
                        ],
                    )
                else:
                    slack_client.chat_postEphemeral(
                        channel=channel_id,
                        user=user_id,
                        text=f"❌ {message}",
                    )

                return {"ok": True}

            except Exception as e:
                logger.error(f"Error handling delete schedule button: {e}")
                slack_client.chat_postEphemeral(
                    channel=channel_id,
                    user=user_id,
                    text="❌ An error occurred while deleting the schedule. Please try again.",
                )
                return {"ok": True}

        else:
            raise ValueError(f"Unknown action_id: {action_id}")

    except Exception as e:
        logger.error(f"Error processing interaction: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
