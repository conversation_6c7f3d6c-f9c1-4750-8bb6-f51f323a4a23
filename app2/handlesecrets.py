import json
import logging

import boto3
from botocore.exceptions import ClientError

logger = logging.getLogger(__name__)


def get_secret(secret_arn: str = None):
    """
    Retrieve secrets from AWS Secrets Manager

    Args:
        secret_arn (str, optional): The ARN of the secret to retrieve. If not provided,
                                  uses the default ARN from config.
    """
    secret_name = secret_arn
    region_name = "us-east-1"

    logger.info(
        f"Attempting to fetch secrets from {secret_name} in region {region_name}"
    )

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(service_name="secretsmanager", region_name=region_name)

    try:
        get_secret_value_response = client.get_secret_value(SecretId=secret_name)
        logger.info("Successfully retrieved secret from AWS Secrets Manager")
    except ClientError as e:
        logger.error(f"Error retrieving secret: {str(e)}")
        raise e
    else:
        if "SecretString" in get_secret_value_response:
            secret = json.loads(get_secret_value_response["SecretString"])
            # Convert all keys to lowercase to match Pydantic model field names
            secret = {k.lower(): v for k, v in secret.items()}
            logger.info(f"Retrieved and normalized secret keys: {list(secret.keys())}")
            return secret
        else:
            logger.error("No SecretString found in response")
            raise ValueError("No SecretString found in response")
