import logging
from langchain_core.messages import AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import ChatOpenAI
from langgraph.types import Command
from typing_extensions import Literal
from app2.resources.prompt_templates import (
    SupervisorAgentPromptTemplate,
    DefaultAgentPromptTemplate,
)
from app2.chatbot.core.states import CustomState
from app2.chatbot.tools.schemas import AgentResponseSchema
from app2.config import Settings
from app2.databricks.databricks_client import DatabricksAgent
from app2.chatbot.core.utils import (
    extract_metadata,
    generate_slack_table_blocks,
)
import json
import boto3
import time

app_config = Settings()
llm = ChatOpenAI(model="gpt-4o-mini", temperature=0, api_key=app_config.openai_api_key)
s3_client = boto3.client("s3")


def supervisor_agent(
    state: CustomState,
) -> Command[Literal["genie_agent", "default_agent", "__end__"]]:
    """Supervisor agent that determine the next step to continue"""

    if state["default_agent_answer"]:
        logging.info(
            "Inside supervisor_agent --> response with -> default_agent_answer"
        )
        return Command(
            update={
                "messages": state["messages"],
                "memory": state["memory"],
                "default_agent_answer": state["default_agent_answer"],  # json
                "genie_query_generated": state["genie_query_generated"],
                "genie_data_answer": state["genie_data_answer"],
                "formatter_file": state["formatter_file"],
                "formatter_summary": state["formatter_summary"],
            },
            goto="__end__",
        )

    if state["formatter_file"]:
        logging.info("Inside supervisor_agent --> response with -> formatter_file")
        return Command(
            update={
                "messages": state["messages"],
                "memory": state["memory"],
                "default_agent_answer": state["default_agent_answer"],
                "genie_query_generated": state["genie_query_generated"],
                "genie_data_answer": state["genie_data_answer"],  # json
                "formatter_file": state["formatter_file"],
                "formatter_summary": state["formatter_summary"],
            },
            goto="__end__",
        )

    llm_with_structure_output = llm.with_structured_output(AgentResponseSchema)
    prompt_template = ChatPromptTemplate.from_messages(
        [
            ("system", SupervisorAgentPromptTemplate.system_prompt.template),
            ("user", SupervisorAgentPromptTemplate.user_prompt.template),
        ]
    )
    chain = prompt_template | llm_with_structure_output
    result = chain.invoke(
        {"user_message": state["messages"], "memory": state["memory"]}
    )
    logging.info(f"supervisor_agente go to.... {result.next_step.strip()}")
    logging.info(
        f"supervisor_agente rewritten_question is.... {result.rewritten_question.strip()}"
    )

    if result.next_step.strip() not in ["genie_agent", "default_agent"]:
        logging.error(
            f"---> [Error]--> Agent decision incorrectly: {result.next_step.strip()}. This should be 'genie_agent' or 'default_agent'"
        )
        raise ValueError(
            f"---> [Error]--> Agent decision incorrectly: {result.next_step.strip()}. This should be 'genie_agent' or 'default_agent'"
        )

    if result.next_step.strip() == "genie_agent":
        # Append the rewritten question to messages instead of replacing them
        updated_messages = state["messages"].copy()
        updated_messages.append(AIMessage(content=result.rewritten_question.strip()))
        return Command(
            update={
                "messages": updated_messages,
                "memory": state["memory"],
                "default_agent_answer": state["default_agent_answer"],
                "genie_query_generated": state["genie_query_generated"],
                "genie_data_answer": state["genie_data_answer"],
                "formatter_file": state["formatter_file"],
                "formatter_summary": state["formatter_summary"],
            },
            goto=result.next_step,
        )

    if result.next_step.strip() == "default_agent":
        # Append the rewritten question to messages instead of replacing them
        updated_messages = state["messages"].copy()
        updated_messages.append(AIMessage(content=result.rewritten_question.strip()))
        return Command(
            update={
                "messages": updated_messages,
                "memory": state["memory"],
                "default_agent_answer": state["default_agent_answer"],
                "genie_query_generated": state["genie_query_generated"],
                "genie_data_answer": state["genie_data_answer"],
                "formatter_file": state["formatter_file"],
                "formatter_summary": state["formatter_summary"],
            },
            goto=result.next_step,
        )


def default_agent(state: CustomState) -> Command[Literal["supervisor_agent"]]:
    """Default agent that always answer question that is not related in any way to Databricks or related."""
    logging.info("Inside Default Agent....")
    prompt_template = ChatPromptTemplate.from_messages(
        [
            ("system", DefaultAgentPromptTemplate.system_memory_prompt.template),
            (
                "system",
                DefaultAgentPromptTemplate.system_previuos_conversations_prompt.template,
            ),
            ("system", DefaultAgentPromptTemplate.system_prompt.template),
            ("user", DefaultAgentPromptTemplate.user_prompt.template),
        ]
    )

    chain = prompt_template | llm
    result = chain.invoke(
        {
            "user_message": state["messages"][-1].content,
            "memory": state["memory"],
        }
    )

    return Command(
        update={
            "messages": [result],
            "memory": state["memory"],
            "default_agent_answer": result.content,
            "genie_query_generated": state["genie_query_generated"],
            "genie_data_answer": state["genie_data_answer"],
            "formatter_file": state["formatter_file"],
            "formatter_summary": state["formatter_summary"],
        },
        goto="supervisor_agent",
    )


def genie_agent(state: CustomState) -> Command[Literal["formatter_agent"]]:
    """This is the Databricks Genie Agent"""

    logging.info("Inside Genie Agent....")
    # Use the last message which contains the rewritten question from supervisor
    user_message = state["messages"][-1].content
    logging.info(f" genie_agent -> user message: {user_message}")
    genie_agent = DatabricksAgent(user_message)
    (query_result, data_description, genie_data_answer) = genie_agent.execute()

    return Command(
        update={
            "messages": state["messages"],
            "memory": state["memory"],
            "default_agent_answer": state["default_agent_answer"],
            "genie_query_generated": query_result,  # sql_query
            "genie_data_answer": genie_data_answer,  # json
            "formatter_file": state["formatter_file"],
            "formatter_summary": data_description,  # data_description
        },
        goto="formatter_agent",
    )


def formatter_agent(state: CustomState) -> Command[Literal["supervisor_agent"]]:
    """This agent make data structure in a efficient format"""

    logger = logging.getLogger(__name__)
    logger.info("Inside Formatter Agent....")
    genie_data_answer = state["genie_data_answer"]

    # Extract metadata with enhanced error handling
    column_names, data_array, status_message = extract_metadata(genie_data_answer)

    # Log extraction results
    logger.info("Extracted metadata from Genie data")
    logger.info(f"Column names: {column_names}")
    logger.info(f"Data array rows: {len(data_array)}")
    logger.info(f"Status message: {status_message}")

    # Generate a unique query ID regardless of result status
    query_id = f"query_{int(time.time())}"

    # Handle error cases or empty results
    if status_message:
        # Create a simple message block for Slack with the error/status message
        slack_blocks = [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*{status_message}*\n\nThe query was processed, but no data was returned.",
                },
            }
        ]

        logger.info(f"Returning status message to Slack: {status_message}")

        # Return state with error information
        return Command(
            update={
                "messages": state["messages"],
                "memory": state["memory"],
                "default_agent_answer": status_message,
                "genie_query_generated": state["genie_query_generated"],
                "genie_data_answer": genie_data_answer,
                "formatter_file": {"query_id": query_id, "status": "no_results"},
                "formatter_summary": state["formatter_summary"],
                "slack_blocks": slack_blocks,
            },
            goto="supervisor_agent",
        )

    # Normal case with data - proceed with standard processing
    if column_names and data_array:
        # Generate Slack blocks for table display with AI summary
        slack_blocks = generate_slack_table_blocks(
            column_names, data_array, state["formatter_summary"]
        )
        logger.info("Generated Slack blocks for table display")

        # Generate a unique query ID
        query_id = f"query_{int(time.time())}"

        # Prepare data to store in S3
        s3_data = {
            "query": state["genie_query_generated"],
            "data_description": state["formatter_summary"],
            "columns": column_names,
            "data": data_array,
            "slack_blocks": slack_blocks,
            "query_id": query_id,  # Add query_id to the stored data
        }

        # Log the complete JSON data in a readable format
        logger.info("\n" + "=" * 50)
        logger.info("COMPLETE JSON DATA FOR S3 STORAGE")
        logger.info("=" * 50)
        logger.info("\n" + json.dumps(s3_data, indent=2))
        logger.info("\n" + "=" * 50 + "\n")

        # Store data in S3
        try:
            s3_client.put_object(
                Bucket=app_config.s3_bucket_name,
                Key=f"results/{query_id}.json",
                Body=json.dumps(s3_data),
                ContentType="application/json",
            )
            logging.info(f"Stored query results in S3 with ID: {query_id}")

            # Generate and log the presigned URL
            presigned_url = s3_client.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": app_config.s3_bucket_name,
                    "Key": f"results/{query_id}.json",
                },
                ExpiresIn=604800,  # 1 week
            )
            logging.info("\n" + "=" * 50)
            logging.info("PRESIGNED URL FOR DOWNLOAD")
            logging.info("=" * 50)
            logging.info(f"\n{presigned_url}")
            logging.info("\n" + "=" * 50 + "\n")

        except Exception as e:
            logging.error(f"Failed to store data in S3: {str(e)}")

        # Return the state with query_id for the supervisor
        return Command(
            update={
                "messages": state["messages"],
                "memory": state["memory"],
                "default_agent_answer": state["default_agent_answer"],
                "genie_query_generated": state["genie_query_generated"],
                "genie_data_answer": state["genie_data_answer"],
                "formatter_file": {"query_id": query_id},  # Return as dictionary
                "formatter_summary": state["formatter_summary"],
                "slack_blocks": slack_blocks,
            },
            goto="supervisor_agent",
        )
