import json
import logging
import tempfile
from typing import List, Dict, Any

import pandas as pd
from langchain_core.messages import ToolMessage
from prettytable import PrettyTable


class BasicToolNode:
    """A node that runs the tools requested in the last AIMessage."""

    def __init__(self, tools: list) -> None:
        self.tools_by_name = {tool.name: tool for tool in tools}

    def __call__(self, inputs: dict):
        if messages := inputs.get("messages", []):
            message = messages[-1]
        else:
            raise ValueError("No message found in input")

        outputs = []
        for tool_call in message.tool_calls:
            tool_result = self.tools_by_name[tool_call["name"]].invoke(
                tool_call["args"]
            )
            outputs.append(
                ToolMessage(
                    content=json.dumps(tool_result),
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
        return {"messages": outputs}


def format_table_with_prettytable(genie_data_answer: dict) -> str:
    """Format data into a structured table using PrettyTable."""

    try:
        columns = genie_data_answer["statement_response"]["manifest"]["schema"][
            "columns"
        ]
        data = genie_data_answer["statement_response"]["result"]["data_array"]
        column_names = [col["name"] for col in columns]

        table = PrettyTable()
        table.field_names = column_names

        for row in data:
            table.add_row(row)

        return f"```\n{table}\n```"

    except KeyError as e:
        return f"Error: Missing key in data structure - {e}"
    except Exception as e:
        return f"Unexpected error: {str(e)}"


def extract_metadata(genie_data: dict) -> tuple:
    """Extract useful metadata with enhanced error handling

    Args:
        genie_data: Response from Databricks Genie API

    Returns:
        tuple: (column_names, data_array, status_message)
        - column_names: List of column names from schema
        - data_array: Data rows or empty list
        - status_message: None if successful, error message if not
    """
    logging.info(f"Extracting metadata from Genie data: {genie_data}")

    # Check if the data is empty or None
    if not genie_data:
        error_msg = "Error: Query processing failed - empty response from Databricks"
        logging.error(error_msg)
        return [], [], error_msg

    try:
        # Check if we have a proper statement_response structure
        if "statement_response" not in genie_data:
            error_msg = "Error: Invalid response format from Databricks"
            logging.error(error_msg)
            return [], [], error_msg

        # Check the status if available
        status = (
            genie_data.get("statement_response", {}).get("status", {}).get("state", "")
        )
        if status == "FAILED":
            error_msg = "Error: Query execution failed in Databricks"
            logging.error(f"{error_msg} - Status: {status}")
            return [], [], error_msg

        # Get schema information
        schema = genie_data["statement_response"]["manifest"]["schema"].get(
            "columns", []
        )
        column_names = [col["name"] for col in schema]

        # Check if result exists and has data_array
        result = genie_data["statement_response"].get("result", {})
        if "data_array" not in result:
            # Query successful but no data (empty result)
            logging.info("Query completed successfully but no matching records found")
            return (
                column_names,
                [],
                "I ran the query successfully but returned no results. Want to give it another shot with a different ask?",
            )

        # Normal case with data
        data_array = result.get("data_array", [])
        if not data_array or (len(data_array) == 1 and not data_array[0]):
            # Empty data array
            logging.info("Query returned empty data array")
            return (
                column_names,
                [],
                "I ran the query successfully but returned no results. Want to give it another shot with a different ask?",
            )

        return column_names, data_array, None

    except Exception as e:
        error_msg = f"Error processing query results: {str(e)}"
        logging.error(f"{error_msg}")
        return [], [], error_msg


def json_to_dataframe(column_names: List[str], data_array: List[list]) -> pd.DataFrame:
    """Transform metadata to dataframe structure"""
    try:
        df = pd.DataFrame(data_array, columns=column_names)
        return df
    except Exception:
        logging.info("Error generating Dataframe...")


def save_dataframe_temp(df: pd.DataFrame) -> str:
    """Save dataframe and get its temporary path"""
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
    df.to_excel(temp_file.name, index=False)
    return temp_file.name


def format_number(value: Any) -> str:
    """Format numbers with thousands separator and decimal point.

    Args:
        value: The value to format

    Returns:
        Formatted string
    """
    try:
        # Try to convert to float first to handle both integers and floats
        num = float(value)
        if num.is_integer():
            # If it's a whole number, format without decimals
            return f"{int(num):,}"
        else:
            # If it has decimals, format with 2 decimal places
            return f"{num:,.2f}"
    except (ValueError, TypeError):
        # If it's not a number, return as string
        return str(value)


def generate_slack_table_blocks(
    column_names: List[str], data_array: List[list], ai_summary: str = ""
) -> List[Dict[str, Any]]:
    """Generate Slack blocks to display data in a table format.

    Args:
        column_names: List of column names
        data_array: List of data rows
        ai_summary: AI generated summary to display before the table

    Returns:
        List of Slack blocks representing the table, or empty list if table has more than 5 columns
    """
    # Initialize blocks array
    blocks = []

    # Handle empty data case first - this can be when there are column names but no data
    if column_names and not data_array:
        # Create a message indicating no results were found
        no_data_message = ai_summary if ai_summary else ""
        no_data_message += "\n\n*Query completed — no matching records found.*"

        blocks.append(
            {"type": "section", "text": {"type": "mrkdwn", "text": no_data_message}}
        )
        return blocks

    # Check if table has more than 5 columns
    if len(column_names) > 5:
        return []  # Return empty list to indicate no blocks should be shown

    # Add summary block if provided - no length constraints for AI summary
    if ai_summary:
        blocks.append(
            {"type": "section", "text": {"type": "mrkdwn", "text": ai_summary}}
        )

    # Constants for table formatting
    MAX_LINE_LENGTH = 80  # Maximum characters per line for table only
    ELLIPSIS = "..."  # Ellipsis for truncated lines
    COLUMN_SPACING = "  "  # Two spaces between columns
    MAX_TABLE_LENGTH = 2500  # Maximum length for the entire table text

    # Function to format any row (header or data) with consistent alignment
    def format_row(row_data, is_header=False):
        # First try to fit everything without truncation
        full_line = ""
        for i, cell in enumerate(row_data):
            cell_content = str(cell)
            # For non-last columns, use the calculated width
            if i < len(row_data) - 1:
                full_line += cell_content.ljust(col_widths[i]) + COLUMN_SPACING
            else:
                # For last column, just add the content
                full_line += cell_content

        # If the full line fits, use it
        if len(full_line) <= MAX_LINE_LENGTH:
            return full_line

        # If it doesn't fit, preserve all columns except the last one
        preserved_content = ""
        for i in range(len(row_data) - 1):
            cell_content = str(row_data[i])
            preserved_content += cell_content.ljust(col_widths[i]) + COLUMN_SPACING

        # Calculate remaining space for last column
        remaining_space = MAX_LINE_LENGTH - len(preserved_content)
        last_column = str(row_data[-1])

        # Add last column with potential truncation
        if remaining_space > 0:
            if len(last_column) <= remaining_space:
                # If last column fits completely, use it
                return preserved_content + last_column
            else:
                # If last column needs truncation
                truncated_line = preserved_content + last_column[:remaining_space]
                # Only add ellipsis if the last two characters aren't spaces
                if not truncated_line[-2:] == "  ":
                    truncated_line = (
                        preserved_content
                        + last_column[: remaining_space - len(ELLIPSIS)]
                        + ELLIPSIS
                    )
                return truncated_line

        return preserved_content.rstrip()

    # Pre-format all data to avoid reformatting
    formatted_data = []
    for row in data_array:
        formatted_row = [format_number(cell) for cell in row]
        formatted_data.append(formatted_row)

    # Calculate column widths based on the full content of headers and data
    col_widths = []
    for i in range(len(column_names)):
        # Start with header width
        max_width = len(str(column_names[i]))
        # Check data widths
        for row in formatted_data:
            max_width = max(max_width, len(str(row[i])))
        col_widths.append(max_width)

    # Create the complete table in a single monospace block
    table_text = "```"  # Start code block for monospace font

    # Add header
    table_text += format_row(column_names, is_header=True) + "\n"

    # Add separator line (respecting MAX_LINE_LENGTH)
    separator_length = min(
        sum(col_widths)
        + (len(col_widths) - 1) * len(COLUMN_SPACING),  # Total width including spaces
        MAX_LINE_LENGTH,  # Maximum allowed length
    )
    separator = "-" * separator_length
    if len(separator) > MAX_LINE_LENGTH:
        separator = separator[:MAX_LINE_LENGTH]
    table_text += separator + "\n"

    # Add all data rows with blank lines between them
    current_length = len(table_text)
    for row in formatted_data:
        row_text = format_row(row)
        # Check if adding this row would exceed the limit
        if current_length + len(row_text) + 2 > MAX_TABLE_LENGTH:  # +2 for newlines
            table_text += "... (more rows available in the detailed view)\n"
            break
        table_text += row_text + "\n\n"  # Add extra newline for spacing
        current_length += len(row_text) + 2  # +2 for newlines

    # Remove the last extra newline to avoid too much spacing at the end
    if table_text.endswith("\n\n"):
        table_text = table_text[:-1]
    table_text += "```"

    # Add the table block
    blocks.append({"type": "section", "text": {"type": "mrkdwn", "text": table_text}})

    return blocks
