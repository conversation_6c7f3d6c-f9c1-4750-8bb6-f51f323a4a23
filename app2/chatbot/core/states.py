from langgraph.graph.message import add_messages
from langchain_core.messages import BaseMessage
from typing import TypedDict, Annotated, Sequence, List, Dict, Any


class CustomState(TypedDict):
    messages: Annotated[Sequence[BaseMessage], add_messages]
    memory: str
    default_agent_answer: str
    genie_query_generated: str
    genie_data_answer: dict
    formatter_file: str
    formatter_summary: str
    slack_blocks: List[Dict[str, Any]]
