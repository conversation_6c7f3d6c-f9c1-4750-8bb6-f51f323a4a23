import logging
from langgraph.graph.graph import START
from langgraph.graph import StateGraph
from langchain_core.messages import HumanMessage
from app2.chatbot.core.states import CustomState
from typing import Dict, Any
from app2.chatbot.core.nodes import (
    supervisor_agent,
    default_agent,
    genie_agent,
    formatter_agent,
)


class Graph:
    workflow: StateGraph

    def __init__(self) -> None:
        self.workflow = StateGraph(CustomState)
        self.__set_nodes()
        self.__set_edges()
        self.graph = self.workflow.compile()

    def __set_nodes(self) -> None:
        """
        Defines the nodes for the workflow, including the AI agent and tool nodes.
        """
        workflow = self.workflow
        workflow.add_node("supervisor_agent", supervisor_agent)
        workflow.add_node("default_agent", default_agent)
        workflow.add_node("genie_agent", genie_agent)
        workflow.add_node("formatter_agent", formatter_agent)

    def __set_edges(self) -> None:
        """
        Defines the edges and conditional transitions between nodes in the workflow.
        """
        workflow = self.workflow
        workflow.add_edge(START, "supervisor_agent")

    def execute_agent(self, message: str, memory: str) -> str:
        """
        Executes the AI agent workflow with the given user message and memory context.

        Args:
            message (str): The user's input message.
            memory (str): The stored conversation history.

        Returns:
            str: The AI-generated response or an error message.
        """
        try:
            logging.info(f"User message: {message}")
            logging.info(f"Memory: {memory}")

            final_state: Dict[str, Any] = self.graph.invoke(
                {
                    "messages": [HumanMessage(content=message)],
                    "memory": memory,
                    "default_agent_answer": "",
                    "genie_query_generated": "",
                    "genie_data_answer": "",
                    "formatter_file": "",
                    "formatter_summary": "",
                    "slack_blocks": [],  # Initialize empty Slack blocks
                }
            )
            if final_state["default_agent_answer"]:
                class_agent: str = "default_agent_answer"
                ai_response: str = final_state["default_agent_answer"]  # str: ai_answer
                ai_summary: str = final_state["formatter_summary"]
                sql_query: str = final_state["genie_query_generated"]
                slack_blocks = final_state["slack_blocks"]  # Get Slack blocks
            elif final_state["formatter_file"]:
                class_agent: str = "formatter_file"
                ai_response: str = final_state["formatter_file"]  # str: temp_path
                ai_summary: str = final_state["formatter_summary"]
                sql_query: str = final_state["genie_query_generated"]
                slack_blocks = final_state["slack_blocks"]  # Get Slack blocks
            else:
                class_agent: str = "Unknown agent"
                ai_response: str = "[Error] final state unrecognized..."
                ai_summary: str = final_state["formatter_summary"]
                sql_query: str = final_state["genie_query_generated"]
                slack_blocks = []  # Empty blocks for error case

            logging.info(f"AI answer: {str(ai_response)}")
            return (
                class_agent,
                ai_response,
                ai_summary,
                sql_query,
                slack_blocks,
            )  # Return Slack blocks

        except Exception as e:
            logging.error(f"Error executing agent: {e}")
            return f"Error Agent Graph: {str(e)}"
