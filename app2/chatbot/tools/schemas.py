from pydantic import BaseModel, Field


class DatabricksSchema(BaseModel):
    user_message: str = Field(
        description="This is a original user message sent by user. Do not modify it"
    )


class APISchema(BaseModel):
    user_message: str = Field(
        description="This is a original user message sent by user. Do not modify it"
    )


class VectorDatabaseSchema(BaseModel):
    user_message: str = Field(
        description="This is a original user message sent by user. Do not modify it"
    )


class AgentResponseSchema(BaseModel):
    """Always use this schema to structure the response and decide whether the next step should be handled by an agent"""

    next_step: str = Field(
        description="Specifies the next step: 'genie_agent' or 'default_agent'. Should be one of them."
    )
    rewritten_question: str = Field(
        description="If the message was a follow-up or required context to interpret, rewrite it into a clear and self-contained question. Otherwise, return the original question."
    )
