# Implement Databricks call if its needed
import logging
from langchain.tools import tool
from app2.chatbot.tools.schemas import VectorDatabaseSchema


@tool(args_schema=VectorDatabaseSchema)
def vector_store_tool(
    query: str,
    # state: Annotated[dict, InjectedState]
):
    """This tool is used to call Vector Store engine"""

    logging.info(f"--> Inside tool: VectorDatabase with query ->: {query}")
    return "Vector Store tool was used but not found nothing"
