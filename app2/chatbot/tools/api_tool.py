# Implement API call if its needed
import logging
from langchain.tools import tool
from app2.chatbot.tools.schemas import APISchema


@tool(args_schema=APISchema)
def api_tool(
    query: str,
    # state: Annotated[dict, InjectedState]
):
    """This tool is used to make an API call."""

    logging.info(f"--> Inside tool: API with query ->: {query}")
    return "API tool was used but not found nothing"
