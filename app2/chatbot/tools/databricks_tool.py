import json
import logging
import time

import requests
from langchain.tools import tool

from app2.chatbot.tools.schemas import DatabricksSchema
from app2.config import Settings

# Create a Settings instance
app_config = Settings()

HEADERS = {
    "Authorization": f"Bearer {app_config.access_token}",
    "Content-Type": "application/json",
}


def create_conversation(user_questions: str) -> tuple:
    """Create Conversation job"""

    URL_INITIAL = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/start-conversation"

    payload = json.dumps({"content": user_questions})
    response = requests.post(URL_INITIAL, headers=HEADERS, data=payload)

    if response.status_code == 200:
        logging.info("--> [1st step] Conversation Created...")
        result = response.json()
        conversation_id = result["conversation_id"]
        message_id = result["message_id"]
        logging.info(f"--> [1st step] conversation_id: {conversation_id}")
        logging.info(f"--> [1st step] message_id: {conversation_id}")
        return conversation_id, message_id
    else:
        logging.info(
            f"--> [1st step] Error Creatin Conversation in Databricks: {response.status_code}, {response.text}"
        )
        return f"[Error] Creating Conversation in Databricks... status_code: {response.status_code}"


def fetch_status_conversation_processed(conversation_id: str, message_id: str) -> str:
    """Fetch Status of Conversation Processed"""

    URL_STATUS = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/conversations/{conversation_id}/messages/{message_id}"

    while True:
        response = requests.get(URL_STATUS, headers=HEADERS)
        if response.status_code == 200:
            result = response.json()
            status = result["status"]
            logging.info(
                f"--> [2nd step] Current Status after conversation created...: {status}"
            )

            if status == "COMPLETED":
                logging.info(
                    "--> [2nd step] Current Status after conversation created...: COMPLETED!!"
                )
                break
            elif status in ["FAILED", "ERROR"]:
                logging.info(
                    "--> [2nd step] Current Status after conversation created...: ERROR or FAILED!!"
                )
                break
        else:
            logging.info(
                f"--> [2nd step] Error Requesting Status of Conversation Created...: {response.status_code}, {response.text}"
            )
            break

        time.sleep(2)


def get_sql_created(conversation_id: str, message_id: str) -> str:
    """Get SQL query Formulated by Databricks - Genie"""

    URL_QUERY = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/conversations/{conversation_id}/messages/{message_id}"

    response = requests.get(URL_QUERY, headers=HEADERS)

    if response.status_code == 200:
        result = response.json()
        query_generated = result["attachments"][0]["query"]["query"]
        attachment_id = result["attachments"][0]["attachment_id"]
        logging.info(f"--> [3rd step] Query generated by Genie...: {query_generated}")
        logging.info(
            f"--> [3rd step] Attachment_id generated by Genie...: {attachment_id}"
        )
        return query_generated, attachment_id
    else:
        logging.info(
            f"--> [3rd step] Error response SQL generated by Genie...: {response.status_code}, {response.text}"
        )
        return f"Error response SQL generated by Genie... {response.status_code}"


def get_result_genie(conversation_id: str, message_id: str, attachment_id: str) -> str:
    """Get answer formulated by Databricks - Genie"""

    URL_RESULTS = f"{app_config.databricks_host}/api/2.0/genie/spaces/{app_config.space_id}/conversations/{conversation_id}/messages/{message_id}/attachments/{attachment_id}/query-result"

    response = requests.get(URL_RESULTS, headers=HEADERS)

    if response.status_code == 200:
        result_genie = response.json()
        core_result = result_genie.get("statement_response", {}).get("result", {})
        logging.info(f"--> [4th step] Sucessfully result from Genie...: {result_genie}")
        return core_result
    else:
        logging.info(
            f"--> [4th step] Error getting result from Genie: {response.status_code}, {response.text}"
        )
        return f"Error getting result from Genie: {response.status_code}"


@tool(args_schema=DatabricksSchema)
def databricks_tool(
    user_message: str,
):
    """This tool is used to call Databricks engine"""

    logging.info(f"--> Inside tool: Databricks with query ->: {user_message}")
    conversation_id, message_id = create_conversation(user_questions=user_message)
    fetch_status_conversation_processed(conversation_id, message_id)
    query_generated, attachment_id = get_sql_created(conversation_id, message_id)
    core_result = get_result_genie(conversation_id, message_id, attachment_id)

    return f"The user message is: {user_message}.\nThe query generated is: {query_generated}.\nThe result data from databricks is: {core_result}.\nOBS: Interpret this in a easy way for non-technical users"
