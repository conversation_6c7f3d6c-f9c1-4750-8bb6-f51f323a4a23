import boto3
import json
from typing import Optional
from uuid import UUID
import logging
from datetime import datetime

from app2.config import Settings
from app2.schemas.recurring_schedule import RecurringScheduleInDB
from app2.utils import is_running_in_lambda

settings = Settings()
logger = logging.getLogger(__name__)


class EventBridgeService:
    def __init__(self):
        # In Lambda, we use IAM role credentials automatically
        # In local development, we need explicit credentials
        is_lambda = is_running_in_lambda()

        if is_lambda:
            # In Lambda environment, check for required ARNs only
            # AWS credentials are provided automatically via IAM role
            self.is_configured = all(
                [
                    settings.lambda_arn,
                    settings.eventbridge_role_arn,
                ]
            )

            if self.is_configured:
                logging.info("Running in Lambda environment with IAM role credentials")
                logging.info(
                    f"AWS Configuration - Region: {settings.aws_region}, Lambda ARN configured: {'Yes' if settings.lambda_arn else 'No'}, Role ARN configured: {'Yes' if settings.eventbridge_role_arn else 'No'}"
                )

                try:
                    # Use default credentials (IAM role) in Lambda
                    self.events_client = boto3.client(
                        "events",
                        region_name=settings.aws_region,
                    )
                    self.scheduler_client = boto3.client(
                        "scheduler",
                        region_name=settings.aws_region,
                    )
                    # Test the AWS credentials
                    self.events_client.list_rules(Limit=1)
                    logging.info(
                        "Successfully connected to AWS EventBridge using IAM role"
                    )
                    self.lambda_arn = settings.lambda_arn
                except Exception as e:
                    logging.error(
                        f"Failed to initialize AWS EventBridge client: {str(e)}"
                    )
                    self.is_configured = False
                    self.events_client = None
                    self.scheduler_client = None
                    self.lambda_arn = None
            else:
                missing_configs = []
                if not settings.lambda_arn:
                    missing_configs.append("Lambda ARN")
                if not settings.eventbridge_role_arn:
                    missing_configs.append("EventBridge Role ARN")

                logging.warning(
                    f"AWS configuration incomplete in Lambda. Missing: {', '.join(missing_configs)}. "
                    "EventBridge service will run in local development mode."
                )
                self.events_client = None
                self.scheduler_client = None
                self.lambda_arn = None
        else:
            # In local development, check for explicit credentials
            self.is_configured = all(
                [
                    settings.aws_access_key_id,
                    settings.aws_secret_access_key,
                    settings.lambda_arn,
                    settings.eventbridge_role_arn,
                ]
            )

            if self.is_configured:
                logging.info(
                    "Running in local development with explicit AWS credentials"
                )
                logging.info(
                    f"AWS Configuration - Region: {settings.aws_region}, Lambda ARN configured: {'Yes' if settings.lambda_arn else 'No'}, Role ARN configured: {'Yes' if settings.eventbridge_role_arn else 'No'}"
                )

                try:
                    self.events_client = boto3.client(
                        "events",
                        aws_access_key_id=settings.aws_access_key_id,
                        aws_secret_access_key=settings.aws_secret_access_key,
                        region_name=settings.aws_region,
                    )
                    self.scheduler_client = boto3.client(
                        "scheduler",
                        aws_access_key_id=settings.aws_access_key_id,
                        aws_secret_access_key=settings.aws_secret_access_key,
                        region_name=settings.aws_region,
                    )
                    # Test the AWS credentials
                    self.events_client.list_rules(Limit=1)
                    logging.info(
                        "Successfully connected to AWS EventBridge with explicit credentials"
                    )
                    self.lambda_arn = settings.lambda_arn
                except Exception as e:
                    logging.error(
                        f"Failed to initialize AWS EventBridge client: {str(e)}"
                    )
                    self.is_configured = False
                    self.events_client = None
                    self.scheduler_client = None
                    self.lambda_arn = None
            else:
                missing_configs = []
                if not settings.aws_access_key_id:
                    missing_configs.append("AWS Access Key ID")
                if not settings.aws_secret_access_key:
                    missing_configs.append("AWS Secret Access Key")
                if not settings.lambda_arn:
                    missing_configs.append("Lambda ARN")
                if not settings.eventbridge_role_arn:
                    missing_configs.append("EventBridge Role ARN")

                logging.warning(
                    f"AWS credentials not configured for local development. Missing: {', '.join(missing_configs)}. "
                    "EventBridge service will run in local development mode."
                )
                self.events_client = None
                self.scheduler_client = None
                self.lambda_arn = None

    def create_rule(self, schedule: RecurringScheduleInDB) -> Optional[str]:
        """Create an EventBridge rule for a schedule (recurring or one-time)."""
        if not self.is_configured:
            logging.info(
                "Running in local development mode - EventBridge rule not created"
            )
            return f"local-development-rule-{schedule.id}"

        try:
            # Determine if this is a one-time or recurring schedule
            if self._is_one_time_schedule(schedule.cron_expression):
                # For one-time schedules, use EventBridge Scheduler
                return self._create_scheduler_schedule(schedule)
            else:
                # For recurring schedules, use EventBridge Rules
                return self._create_eventbridge_rule(schedule)

        except Exception as e:
            logging.error(f"Error creating EventBridge rule/schedule: {str(e)}")
            return None

    def _create_eventbridge_rule(
        self, schedule: RecurringScheduleInDB
    ) -> Optional[str]:
        """Create an EventBridge rule for recurring schedules."""
        rule_name = f"slack-schedule-{schedule.id}"
        logging.info(f"Creating EventBridge rule: {rule_name}")

        response = self.events_client.put_rule(
            Name=rule_name,
            ScheduleExpression=f"cron({schedule.cron_expression})",
            State="ENABLED",
            Description=f"Recurring Slack message for user {schedule.user_id}",
            RoleArn=settings.eventbridge_role_arn,
        )
        logging.info(f"Successfully created EventBridge rule: {rule_name}")

        # Create the target
        target_input = {
            "schedule_id": str(schedule.id),
            "channel_id": schedule.channel_id,
            "user_id": schedule.user_id,
            "text": schedule.message,
            "is_scheduled": True,
            "type": "message",
        }
        logging.info(
            f"Setting up target for rule {rule_name} with Lambda ARN: {self.lambda_arn}"
        )
        logging.info(f"Target input payload: {json.dumps(target_input)}")

        try:
            target_response = self.events_client.put_targets(
                Rule=rule_name,
                Targets=[
                    {
                        "Id": f"slack-target-{schedule.id}",
                        "Arn": self.lambda_arn,
                        "Input": json.dumps(target_input),
                    }
                ],
            )
            logging.info(
                f"Successfully set up target. Response: {json.dumps(target_response)}"
            )
            return response["RuleArn"]
        except Exception as target_error:
            logging.error(
                f"Failed to set up target for rule {rule_name}: {str(target_error)}"
            )
            # Clean up the rule since target setup failed
            try:
                self.events_client.delete_rule(Name=rule_name)
                logging.info(f"Cleaned up rule {rule_name} after target setup failure")
            except Exception as cleanup_error:
                logging.error(
                    f"Failed to clean up rule after target setup failure: {str(cleanup_error)}"
                )
            return None

    def _create_scheduler_schedule(
        self, schedule: RecurringScheduleInDB
    ) -> Optional[str]:
        """Create an EventBridge Scheduler schedule for one-time schedules."""
        schedule_name = f"slack-schedule-{schedule.id}"
        logging.info(f"Creating EventBridge Scheduler schedule: {schedule_name}")

        # Use at() expression for EventBridge Scheduler
        schedule_expression = f"at({schedule.cron_expression})"
        logging.info(f"One-time schedule expression: {schedule_expression}")

        target_input = {
            "schedule_id": str(schedule.id),
            "channel_id": schedule.channel_id,
            "user_id": schedule.user_id,
            "text": schedule.message,
            "is_scheduled": True,
            "type": "message",
        }

        try:
            response = self.scheduler_client.create_schedule(
                Name=schedule_name,
                ScheduleExpression=schedule_expression,
                Target={
                    "Arn": self.lambda_arn,
                    "RoleArn": settings.eventbridge_role_arn,
                    "Input": json.dumps(target_input),
                },
                FlexibleTimeWindow={"Mode": "OFF"},
                Description=f"One-time Slack message for user {schedule.user_id}",
            )
            logging.info(
                f"Successfully created EventBridge Scheduler schedule: {schedule_name}"
            )
            return response["ScheduleArn"]
        except Exception as e:
            logging.error(f"Failed to create EventBridge Scheduler schedule: {str(e)}")
            return None

    def create_one_time_rule(
        self,
        schedule_id: UUID,
        channel_id: str,
        user_id: str,
        message: str,
        timestamp: int,
    ) -> Optional[str]:
        """Create an EventBridge Scheduler schedule for a one-time schedule using timestamp."""
        if not self.is_configured:
            logging.info(
                "Running in local development mode - EventBridge schedule not created"
            )
            return f"local-development-schedule-{schedule_id}"

        try:
            # Create the schedule
            schedule_name = f"slack-schedule-{schedule_id}"
            logging.info(
                f"Creating one-time EventBridge Scheduler schedule: {schedule_name}"
            )

            # Convert timestamp to ISO 8601 format for EventBridge Scheduler 'at()' expression
            dt = datetime.fromtimestamp(timestamp)
            iso_time = dt.strftime("%Y-%m-%dT%H:%M:%S")
            schedule_expression = f"at({iso_time})"
            logging.info(f"One-time schedule expression: {schedule_expression}")

            target_input = {
                "schedule_id": str(schedule_id),
                "channel_id": channel_id,
                "user_id": user_id,
                "text": message,
                "is_scheduled": True,
                "type": "message",
            }

            response = self.scheduler_client.create_schedule(
                Name=schedule_name,
                ScheduleExpression=schedule_expression,
                Target={
                    "Arn": self.lambda_arn,
                    "RoleArn": settings.eventbridge_role_arn,
                    "Input": json.dumps(target_input),
                },
                FlexibleTimeWindow={"Mode": "OFF"},
                Description=f"One-time Slack message for user {user_id}",
            )
            logging.info(
                f"Successfully created EventBridge Scheduler schedule: {schedule_name}"
            )
            return response["ScheduleArn"]

        except Exception as e:
            logging.error(
                f"Error creating one-time EventBridge Scheduler schedule: {str(e)}"
            )
            return None

    def _is_one_time_schedule(self, cron_expression: str) -> bool:
        """Check if a cron expression represents a one-time schedule (ISO timestamp)."""
        # One-time schedules will have ISO timestamp format instead of cron format
        try:
            # Try to parse as ISO timestamp
            datetime.fromisoformat(cron_expression.replace("Z", "+00:00"))
            return True
        except ValueError:
            return False

    def update_rule(self, schedule: RecurringScheduleInDB) -> bool:
        """Update an existing EventBridge rule."""
        if not self.is_configured:
            logging.info(
                "Running in local development mode - EventBridge rule not updated"
            )
            return True

        try:
            if self._is_one_time_schedule(schedule.cron_expression):
                # One-time schedules using EventBridge Scheduler cannot be updated
                # They need to be deleted and recreated
                logging.warning(
                    "One-time schedules cannot be updated. Delete and recreate instead."
                )
                return False

            rule_name = f"slack-schedule-{schedule.id}"
            logging.info(f"Updating EventBridge rule: {rule_name}")

            # Update the rule
            self.events_client.put_rule(
                Name=rule_name,
                ScheduleExpression=f"cron({schedule.cron_expression})",
                State="ENABLED" if schedule.is_active else "DISABLED",
                Description=f"Recurring Slack message for user {schedule.user_id}",
                RoleArn=settings.eventbridge_role_arn,
            )
            logging.info(f"Successfully updated rule: {rule_name}")

            # Update the target
            target_input = {
                "schedule_id": str(schedule.id),
                "channel_id": schedule.channel_id,
                "user_id": schedule.user_id,
                "text": schedule.message,
                "is_scheduled": True,
                "type": "message",
            }
            logging.info(f"Updating target for rule {rule_name}")

            self.events_client.put_targets(
                Rule=rule_name,
                Targets=[
                    {
                        "Id": f"slack-target-{schedule.id}",
                        "Arn": self.lambda_arn,
                        "Input": json.dumps(target_input),
                    }
                ],
            )
            logging.info(f"Successfully updated target for rule {rule_name}")

            return True
        except Exception as e:
            logging.error(f"Error updating EventBridge rule: {str(e)}")
            return False

    def delete_rule(self, schedule_id: UUID) -> bool:
        """Delete an EventBridge rule or EventBridge Scheduler schedule."""
        if not self.is_configured:
            logging.info(
                "Running in local development mode - EventBridge rule not deleted"
            )
            return True

        try:
            rule_name = f"slack-schedule-{schedule_id}"
            schedule_name = f"slack-schedule-{schedule_id}"

            # Try to delete as EventBridge Rule first
            try:
                logging.info(f"Attempting to delete EventBridge rule: {rule_name}")
                # Remove targets first
                self.events_client.remove_targets(
                    Rule=rule_name, Ids=[f"slack-target-{schedule_id}"]
                )
                logging.info(f"Successfully removed targets for rule {rule_name}")

                # Delete the rule
                self.events_client.delete_rule(Name=rule_name)
                logging.info(f"Successfully deleted EventBridge rule {rule_name}")
                return True
            except Exception as rule_error:
                logging.info(
                    f"Rule not found in EventBridge Rules, trying EventBridge Scheduler: {rule_error}"
                )

                # Try to delete as EventBridge Scheduler schedule
                try:
                    self.scheduler_client.delete_schedule(Name=schedule_name)
                    logging.info(
                        f"Successfully deleted EventBridge Scheduler schedule {schedule_name}"
                    )
                    return True
                except Exception as scheduler_error:
                    logging.error(
                        f"Failed to delete from both EventBridge Rules and Scheduler: {scheduler_error}"
                    )
                    return False

        except Exception as e:
            logging.error(f"Error deleting EventBridge rule/schedule: {str(e)}")
            return False

    def parse_cron(self, schedule: str) -> Optional[str]:
        """Convert a human-readable schedule to a cron expression."""
        logging.info(f"Parsing schedule string: {schedule}")

        # This is a simple implementation - you might want to use a more sophisticated parser
        schedule = schedule.lower().strip()

        # Dictionary of weekday names to numbers
        weekdays = {
            "monday": "MON",
            "tuesday": "TUE",
            "wednesday": "WED",
            "thursday": "THU",
            "friday": "FRI",
            "saturday": "SAT",
            "sunday": "SUN",
        }

        try:
            if "every" in schedule:
                # First, find the day in the schedule
                schedule_part = None
                day_code = None

                if "every day" in schedule:
                    schedule_part = "every day"
                else:
                    for day in weekdays:
                        if day in schedule:
                            schedule_part = f"every {day}"
                            day_code = weekdays[day]
                            break

                if not schedule_part:
                    logging.error(f"Could not find valid day in schedule: {schedule}")
                    return None

                # Extract time part (everything after "at")
                time_idx = schedule.find(" at ")
                if time_idx == -1:
                    logging.error(f"Could not find 'at' in schedule: {schedule}")
                    return None

                time_part = schedule[time_idx + 4 :].strip()  # +4 to skip " at "
                logging.info(f"Extracted time part: {time_part}")

                # Parse time
                if "am" in time_part or "pm" in time_part:
                    # Convert 12-hour format to 24-hour
                    time_str = time_part.replace("am", "").replace("pm", "").strip()
                    try:
                        if ":" in time_str:
                            hour, minute = map(int, time_str.split(":"))
                        else:
                            hour = int(time_str)
                            minute = 0

                        if "pm" in time_part.lower() and hour != 12:
                            hour += 12
                        elif "am" in time_part.lower() and hour == 12:
                            hour = 0

                        logging.info(f"Parsed time - Hour: {hour}, Minute: {minute}")
                    except ValueError as e:
                        logging.error(f"Error parsing time {time_str}: {str(e)}")
                        return None
                else:
                    # Assume 24-hour format
                    time_str = time_part.strip()
                    try:
                        if ":" in time_str:
                            hour, minute = map(int, time_str.split(":"))
                        else:
                            hour = int(time_str)
                            minute = 0
                        logging.info(
                            f"Parsed time (24h format) - Hour: {hour}, Minute: {minute}"
                        )
                    except ValueError as e:
                        logging.error(f"Error parsing time {time_str}: {str(e)}")
                        return None

                # Validate hour and minute
                if not (0 <= hour <= 23 and 0 <= minute <= 59):
                    logging.error(
                        f"Invalid time values - hour: {hour}, minute: {minute}"
                    )
                    return None

                # Generate cron expression
                if schedule_part == "every day":
                    cron = f"{minute} {hour} * * ? *"
                else:
                    cron = f"{minute} {hour} ? * {day_code} *"

                logging.info(f"Generated cron expression: {cron}")
                return cron

        except Exception as e:
            logging.error(f"Error parsing schedule '{schedule}': {str(e)}")
            return None

        logging.error(f"Schedule format not recognized: {schedule}")
        return None
