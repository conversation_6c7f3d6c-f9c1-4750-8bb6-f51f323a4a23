# INITIAL STEP

`intial location: "SlackGenie"`

# VERIFY TOKEN SLACK

`curl -H "Authorization: Bear<PERSON> ******************************************************" https://slack.com/api/auth.test`

```
{
    "ok":true,
    "url":"https:\/\/distillery-tech.slack.com\/",
    "team":"Distillery",
    "user":"slackgeniebot",
    "team_id":"T03G61VPV",
    "user_id":"U08H1VC72MN",
    "bot_id":"B08H1VC6Z8Q",
    "is_enterprise_install":false
}%   
```

# ENVIRONMENT SETUP

- `python3.12 -m venv env`
- `source env/bin/activate`
- `pip install -r requirements.txt`

# RUN APP STEPs

1. In one terminal UP and RUN:

`uvicorn app2.main:app --reload --host 0.0.0.0 --port 8000 --log-level info`

2. (after installed ngrok) In second terminal UP and RUN:

`ngrok http 8000`

- (It will generate a `https://<random_code>.ngrok-free.app` URL)

3. Go to SlackAPI: `https://api.slack.com/apps/A08G5BNFKBQ/event-subscriptions?` (could ask permissions, contact to the owner)

4. Once in SlackAPI, go to "Event Subscription". 
    - Into the URL generated (step 2 - url generated by ngrok), you need to add at the end: `/slack/events`
    - Example: `https://68c0-2803-9810-60c7-c10-7d1b-a42a-abad-6b0c.ngrok-free.app/slack/events`
    - Then paste this complete URL into SlackAPI "Event Subscription -> Enable Events -> Request URLs".
    - It will check the new URL pasted and show notification of "Verfied URL" in green color or in yellow if it is wrong.
    - SAVE changes as final step.

5. Once terminals (step 1 and 2) is running well, NOW you can chat whti the bot.
    - In Slack the bot is called `slack_genie_bot` and then ask anything you want.


# INSIGHTS:

## BODY REQUEST SENT BY USER IN SLACK:

```
{
    "token": "uGUNJuU4R5Q7BeA7MaDuMVMi", --->> `unique_code`
    "team_id": "T03G61VPV", --->> `unique_code`
    "context_team_id": "T03G61VPV",
    "context_enterprise_id": None, 
    "api_app_id": "A08G5BNFKBQ", --->> `unique_code`
    "event": { <====
        "user": "U08DKQG1XM3", --->> `unique_code` <====
        "type": "message", 
        "ts": "1741641227.147579", 
        "client_msg_id": "4df1cbb2-1e5d-4107-a416-0550c1c1f648", 
        "text": "Hola, sabes de que pais es Boca Juniors? equipo de futbol" <====
        "team": "T03G61VPV", 
        "blocks": [
            {
                "type": "rich_text", 
                "block_id": "VMyL+", 
                "elements": [
                    {
                        "type": "rich_text_section", 
                        "elements": [
                            {
                                "type": "text", "text": "Hola, sabes de que pais es Boca Juniors? equipo de futbol"
                            }
                        ]
                    }
                ]
            }
        ], 
        "channel": "D08H2H95X5E", --->> `unique_code` <====
        "event_ts": "1741641227.147579", 
        "channel_type": "im"
    }, 
    "type": "event_callback", 
    "event_id": "Ev08GN8TFC5D", <====
    "event_time": 1741641227, 
    "authorizations": [
        {
            "enterprise_id": None, 
            "team_id": "T03G61VPV", 
            "user_id": "U08H1VC72MN", 
            "is_bot": True, 
            "is_enterprise_install": False
        }
    ], 
    "is_ext_shared_channel": False, 
    "event_context": "4-eyJldCI6Im1lc3NhZ2UiLCJ0aWQiOiJUMDNHNjFWUFYiLCJhaWQiOiJBMDhHNUJORktCUSIsImNpZCI6IkQwOEgySDk1WDVFIn0" --->> `unique_code`
}
```

## BOT AUTO-ANSWER --- DIFERENT JSON

- This is a problem some times because of auto-answer.

```
{
    "token": "uGUNJuU4R5Q7BeA7MaDuMVMi", 
    "team_id": "T03G61VPV", 
    "context_team_id": "T03G61VPV", 
    "context_enterprise_id": None, 
    "api_app_id": "A08G5BNFKBQ", 
    "event": {
        "user": "U08H1VC72MN", 
        "type": "message", 
        "ts": "1741644399.080159", 
        "bot_id": "B08H1VC6Z8Q", 
        "app_id": "A08G5BNFKBQ", 
        "text": "Hola <@U08H1VC72MN>, ¡Hola! Así es, la bandera de Perú tiene un diseño muy distintivo. Las franjas rojas representan la sangre derramada por los héroes nacionales, mientras que la franja blanca simboliza la paz y la pureza. El escudo de armas en el centro de la franja blanca incluye elementos que representan la flora y fauna del país, así como la riqueza mineral. Si necesitas más información sobre la bandera o cualquier otro tema relacionado, ¡no dudes en preguntar!", 
        "team": "T03G61VPV", 
        "bot_profile": {
            "id": "B08H1VC6Z8Q", 
            "deleted": False, 
            "name": "slack_genie_bot", 
            "updated": 1741234225, 
            "app_id": "A08G5BNFKBQ", 
            "icons": {
                "image_36": "https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_36.jpg", 
                "image_48": "https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_48.jpg", 
                "image_72": "https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_72.jpg"
            }, 
            "team_id": 
            "T03G61VPV"
        }, 
        "blocks": [
            {
                "type": "rich_text", 
                "block_id": "1ToSG", 
                "elements": [
                    {
                        "type": "rich_text_section", 
                        "elements": [
                            {
                                "type": "text", 
                                "text": "Hola "
                            }, 
                            {
                                "type": "user", 
                                "user_id": "U08H1VC72MN"
                            }, 
                            {
                                "type": "text", 
                                "text": ", ¡Hola! Así es, la bandera de Perú tiene un diseño muy distintivo. Las franjas rojas representan la sangre derramada por los héroes nacionales, mientras que la franja blanca simboliza la paz y la pureza. El escudo de armas en el centro de la franja blanca incluye elementos que representan la flora y fauna del país, así como la riqueza mineral. Si necesitas más información sobre la bandera o cualquier otro tema relacionado, ¡no dudes en preguntar!"
                            }
                        ]
                    }
                ]
            }
        ], 
        "channel": "D08H2H95X5E", 
        "event_ts": "1741644399.080159", 
        "channel_type": "im"
    }, 
    "type": "event_callback", 
    "event_id": "Ev08H519APPV", 
    "event_time": 1741644399, 
    "authorizations": [
        {
            "enterprise_id": None, 
            "team_id": "T03G61VPV", 
            "user_id": "U08H1VC72MN", 
            "is_bot": True, 
            "is_enterprise_install": False
        }
    ], 
    "is_ext_shared_channel": False, 
    "event_context": "4-eyJldCI6Im1lc3NhZ2UiLCJ0aWQiOiJUMDNHNjFWUFYiLCJhaWQiOiJBMDhHNUJORktCUSIsImNpZCI6IkQwOEgySDk1WDVFIn0"
}
```


# ----- Example ----

```
{
    "token": "uGUNJuU4R5Q7BeA7MaDuMVMi",
    "team_id": "T03G61VPV",
    "context_team_id": "T03G61VPV",
    "context_enterprise_id": null, 
    "api_app_id": "A08G5BNFKBQ", 
    "event": {
        "user": "U08DKQG1XM4",
        "type": "message", 
        "ts": "1741641227.147579", 
        "client_msg_id": "4df1cbb2-1e5d-4107-a416-0550c1c1f648", 
        "text": "Sabes quien es Maradona?",
        "team": "T03G61VPV", 
        "blocks": [
            {
                "type": "rich_text", 
                "block_id": "VMyL+", 
                "elements": [
                    {
                        "type": "rich_text_section", 
                        "elements": [
                            {
                                "type": "text", "text": "Sabes quien es Maradona?"
                            }
                        ]
                    }
                ]
            }
        ], 
        "channel": "D08H2H95X5E",
        "event_ts": "1741641227.147579", 
        "channel_type": "im"
    }, 
    "type": "event_callback", 
    "event_id": "Ev08GN8TFC5D",
    "event_time": 1741641227, 
    "authorizations": [
        {
            "enterprise_id": null, 
            "team_id": "T03G61VPV", 
            "user_id": "U08H1VC72MN", 
            "is_bot": true, 
            "is_enterprise_install": false
        }
    ], 
    "is_ext_shared_channel": false, 
    "event_context": "4-eyJldCI6Im1lc3NhZ2UiLCJ0aWQiOiJUMDNHNjFWUFYiLCJhaWQiOiJBMDhHNUJORktCUSIsImNpZCI6IkQwOEgySDk1WDVFIn0"
}
```