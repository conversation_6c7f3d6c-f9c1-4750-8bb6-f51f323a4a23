#!/usr/bin/env python3
"""
Debug script to log all environment variables in Lambda.
This helps troubleshoot environment detection issues.
"""

import os
import logging

logger = logging.getLogger(__name__)


def log_all_environment_variables():
    """Log all environment variables for debugging."""
    logger.info("=== ENVIRONMENT VARIABLES DEBUG ===")

    # Lambda-specific environment variables to highlight
    lambda_env_vars = [
        "AWS_EXECUTION_ENV",
        "AWS_LAMBDA_FUNCTION_NAME",
        "AWS_LAMBDA_FUNCTION_VERSION",
        "AWS_LAMBDA_RUNTIME_API",
        "LAMBDA_RUNTIME_DIR",
        "LAMBDA_TASK_ROOT",
        "_LAMBDA_TELEMETRY_LOG_FD",
        "AWS_LAMBDA_LOG_GROUP_NAME",
        "AWS_LAMBDA_LOG_STREAM_NAME",
        "AWS_REGION",
        "AWS_DEFAULT_REGION",
    ]

    logger.info("Lambda-specific environment variables:")
    for var in lambda_env_vars:
        value = os.environ.get(var, "NOT SET")
        logger.info(f"  {var}: {value}")

    logger.info("\nAll environment variables:")
    all_env_vars = dict(os.environ)

    # Sort for easier reading
    for key in sorted(all_env_vars.keys()):
        value = all_env_vars[key]
        # Mask sensitive values
        if any(
            sensitive in key.upper()
            for sensitive in ["TOKEN", "SECRET", "KEY", "PASSWORD"]
        ):
            masked_value = f"{value[:2]}...{value[-2:]}" if len(value) > 4 else "***"
            logger.info(f"  {key}: {masked_value}")
        else:
            logger.info(f"  {key}: {value}")

    logger.info("=== END ENVIRONMENT VARIABLES DEBUG ===")

    return all_env_vars


def get_lambda_detection_info():
    """Get detailed information about Lambda environment detection."""
    from app2.utils import is_running_in_lambda

    logger.info("=== LAMBDA DETECTION DEBUG ===")

    # Check each indicator individually
    indicators = {
        "AWS_LAMBDA_FUNCTION_NAME": os.environ.get("AWS_LAMBDA_FUNCTION_NAME"),
        "AWS_LAMBDA_FUNCTION_VERSION": os.environ.get("AWS_LAMBDA_FUNCTION_VERSION"),
        "LAMBDA_RUNTIME_DIR": os.environ.get("LAMBDA_RUNTIME_DIR"),
        "AWS_EXECUTION_ENV": os.environ.get("AWS_EXECUTION_ENV"),
        "AWS_EXECUTION_ENV_starts_with_AWS_Lambda": os.environ.get(
            "AWS_EXECUTION_ENV", ""
        ).startswith("AWS_Lambda_"),
    }

    logger.info("Individual Lambda indicators:")
    for key, value in indicators.items():
        logger.info(f"  {key}: {value}")

    # Overall detection result
    is_lambda = is_running_in_lambda()
    logger.info(f"\nFinal Lambda detection result: {is_lambda}")

    logger.info("=== END LAMBDA DETECTION DEBUG ===")

    return {"is_lambda": is_lambda, "indicators": indicators}


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
    )

    print("Logging environment variables...")
    env_vars = log_all_environment_variables()

    print("\nChecking Lambda detection...")
    detection_info = get_lambda_detection_info()

    # Also print to stdout for immediate visibility
    print(f"\nLambda Detection Result: {detection_info['is_lambda']}")
    print(f"AWS_EXECUTION_ENV: {os.environ.get('AWS_EXECUTION_ENV', 'NOT SET')}")
