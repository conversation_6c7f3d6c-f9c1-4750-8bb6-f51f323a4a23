<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detailed Query Results</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Prism.js CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(to right, #0036bd, #9a86ca);
            --light-grey: #f1f5ff;
            --border-color: #e9ecef;
            --text-color: #2c3e50;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--light-grey);
            color: var(--text-color);
        }

        .page-link {
            color: #4e49c2 !important;
            background-color: transparent;
            border-color: transparent;
        }

        .page-link.active, 
        .active > .page-link {
            color: #4e49c2 !important;
            background-color: transparent;
            border-color: transparent;
        }

        .main-container {
            width: 100%;
            min-height: 100vh;
            padding: 0;
        }

        .header {
            background: white;
            padding: 1.5rem 0;
            box-shadow: var(--card-shadow);
            margin-bottom: 2rem;
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
        }

        .logo {
            width: 60px;
            height: 60px;
            margin-right: 20px;
            object-fit: contain;
            flex-shrink: 0;
        }

        .powered-logo {
            width: 350px;
            height: 40px;
            object-fit: contain;
            margin-left: auto;
        }

        .title {
            font-size: 3.5rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin: 0;
            line-height: 1;
            white-space: nowrap;
        }

        .content-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .description-section {
            background-color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
            border-left: 4px solid #0036bd;
        }

        .description-section h5 {
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .table-container {
            background-color: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background-color: var(--light-grey);
            border-bottom: 2px solid var(--border-color);
            font-weight: 600;
            color: var(--text-color);
            padding: 1rem;
        }

        .table thead th:first-child {
            border-top-left-radius: 12px;
        }

        .table thead th:last-child {
            border-top-right-radius: 12px;
        }

        .table tbody td {
            vertical-align: middle;
            padding: 1rem;
            border-color: var(--border-color);
        }

        .table tbody tr:hover {
            background-color: rgba(0, 54, 189, 0.05);
        }

        .query-section {
            background-color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            box-shadow: var(--card-shadow);
        }

        .query-content {
            display: none;
            margin-top: 1rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 8px;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            white-space: pre-wrap;
            font-size: 0.9rem;
        }

        .query-content pre {
            margin: 0;
            padding: 0;
            background: none;
        }

        .query-content code {
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        /* Override Prism.js theme colors for SQL */
        .query-content .token.keyword {
            color: #0036bd;
        }
        .query-content .token.function {
            color: #9a86ca;
        }
        .query-content .token.string {
            color: #2e7d32;
        }
        .query-content .token.number {
            color: #f57c00;
        }
        .query-content .token.comment {
            color: #6c757d;
        }
        .query-content .token.operator {
            color: #0036bd;
        }
        .query-content .token.punctuation {
            color: #2c3e50;
        }

        .toggle-btn {
            cursor: pointer;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 500;
        }

        .toggle-btn:hover {
            opacity: 0.9;
        }

        /* DataTables Customization */
        .dataTables_wrapper {
            padding: 0;
        }

        .dataTables_filter input {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.5rem 1rem;
            margin-left: 0.5rem;
            transition: all 0.3s ease;
            width: 300px!important;
        }

        .dataTables_filter input:focus {
            border-color: #0036bd;
            box-shadow: 0 0 0 0.2rem rgba(0, 54, 189, 0.25);
        }

        .dataTables_length select {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.5rem 2rem 0.5rem 1rem;
            transition: all 0.3s ease;
        }

        .dataTables_length select:focus {
            border-color: #0036bd;
            box-shadow: 0 0 0 0.2rem rgba(0, 54, 189, 0.25);
        }

        .dataTables_info {
            padding-top: 1rem;
            color: var(--text-color);
        }

        .dataTables_paginate {
            padding-top: 1rem;
        }

        .paginate_button {
            padding: 0.5rem 1rem;
            margin: 0 0.25rem;
            border: none !important;
            border-radius: 8px;
            background-color: white;
            transition: all 0.3s ease;
        }

        .paginate_button.current {
            background: var(--primary-gradient);
            color: white !important;
            border: none !important;
        }

        .paginate_button:hover {
            background-color: var(--light-grey);
            color: #0036bd;
            border: none !important;
        }

        .export-btn {
            background: linear-gradient(to right, #1837be, #504ac2);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            opacity: 0.9;
            color: white;
        }

        /* Table controls layout */
        .table-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .table-controls-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .table-controls-right {
            display: flex;
            align-items: center;
        }

        /* Footer styles */
        .footer {
            background-color: white;
            padding: 3rem 0;
            margin-top: 3rem;
            text-align: center;
            box-shadow: 0 -4px 6px rgba(0, 0, 0, 0.1);
        }

        .footer-logo {
            width: 300px;
            height: auto;
            margin-bottom: 1rem;
        }

        .footer-website {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2rem;
            text-decoration: none;
            display: block;
            text-align: center;
        }

        .footer-website:hover {
            color: #0036bd;
        }

        .social-section {
            margin-bottom: 1.5rem;
        }

        .social-text {
            font-size: 1.2rem;
            font-weight: 500;
            color: #2c3e50;
            margin-right: 1rem;
            display: inline-block;
            vertical-align: middle;
        }

        .social-icons {
            display: inline-flex;
            gap: 1rem;
            vertical-align: middle;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(to right, #1837be, #504ac2);
            color: white;
            text-decoration: none;
            transition: transform 0.2s ease;
        }

        .social-icon i {
            font-size: 1.2rem;
            color: white;
        }

        .social-icon:hover {
            transform: translateY(-2px);
        }

        .copyright {
            color: #6c757d;
            font-size: 1rem;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .header-content {
                padding: 0 1rem;
                flex-wrap: wrap;
                justify-content: center;
                text-align: center;
            }

            .content-container {
                padding: 0 1rem;
            }

            .title {
                font-size: 2rem;
                width: 100%;
                text-align: center;
                margin: 0.5rem 0;
            }

            .logo {
                width: 50px;
                height: 50px;
                margin: 0;
            }

            .powered-logo {
                width: 200px;
                margin: 0.5rem 0;
                order: 3;
            }

            .footer {
                padding: 2rem 1rem;
            }

            .footer-logo {
                width: 200px;
            }

            .footer-website {
                font-size: 1.2rem;
            }

            .social-text {
                display: block;
                margin-bottom: 1rem;
                text-align: center;
            }

            .social-icons {
                justify-content: center;
            }

            /* Table responsiveness */
            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin: 0 -1rem;
                padding: 1.5rem 1rem;
                position: relative;
            }

            .table {
                min-width: 100%;
                width: max-content;
            }

            /* Chart responsiveness */
            .chart-controls {
                flex-direction: column;
                gap: 0.5rem;
            }

            .chart-type-select,
            .chart-columns-select {
                width: 100%;
            }

            .chart-container {
                height: 300px;
            }

            /* DataTables controls responsiveness */
            .table-controls {
                flex-direction: row;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1rem;
            }

            .table-controls-left,
            .table-controls-right {
                width: auto;
            }

            .table-controls-left {
                flex: 1;
            }

            .dataTables_filter {
                width: 100%;
                margin: 0;
                padding: 0;
            }

            .dataTables_filter label {
                width: 100%;
                margin: 0;
                display: flex;
                align-items: center;
            }

            .dataTables_filter input {
                width: 100% !important;
                margin: 0;
                margin-left: 0.5rem !important;
            }

            .export-btn {
                width: auto;
                padding: 0.5rem;
                min-width: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .export-btn span.export-text {
                display: none;
            }

            .export-btn i.export-icon {
                display: inline-block;
                font-size: 1.2rem;
            }

            /* Description section responsiveness */
            .description-section {
                margin: 1rem -1rem;
                border-radius: 0;
            }

            /* Query section responsiveness */
            .query-section {
                margin: 1rem -1rem;
                border-radius: 0;
            }

            .query-content {
                font-size: 0.8rem;
                padding: 0.5rem;
                overflow-x: auto;
            }
        }

        /* Additional styles for very small screens */
        @media (max-width: 480px) {
            .title {
                font-size: 1.5rem;
            }

            .dataTables_length,
            .dataTables_info,
            .dataTables_paginate {
                text-align: center;
                width: 100%;
                margin-top: 0.5rem;
            }

            .paginate_button {
                padding: 0.25rem 0.5rem;
            }
        }

        /* Chart section styles */
        .chart-section {
            background-color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: var(--card-shadow);
        }
        
        .chart-container {
            position: relative;
            height: 400px;
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .chart-controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .chart-type-select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: white;
        }
        
        .chart-columns-select {
            padding: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background-color: white;
            min-width: 200px;
        }

        /* Add this before the media queries */
        .export-btn i.export-icon {
            display: none;
        }

        .export-btn span.export-text {
            display: inline;
        }

        @media (max-width: 768px) {
            /* ... existing mobile styles ... */

            .export-btn span.export-text {
                display: none;
            }

            .export-btn i.export-icon {
                display: inline-block;
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="header">
            <div class="header-content">
                <img src="{{ url_for('static', path='images/logo.png') }}" alt="DistillGenie Logo" class="logo">
                <h2 class="title">DistillGenie</h2>
                <img src="{{ url_for('static', path='images/powered.png') }}" alt="Powered By" class="powered-logo">
            </div>
        </div>

        <div class="content-container">
            {% if data_description %}
            <div class="description-section">
                <h5>Description</h5>
                <p class="mb-0">{{ data_description }}</p>
            </div>
            {% endif %}

            {% if data %}
            <div class="chart-section">
                <h5>Data Visualization</h5>
                <div class="chart-controls">
                    <select id="chartType" class="chart-type-select">
                        <option value="bar">Bar Chart</option>
                        <option value="line">Line Chart</option>
                        <option value="pie">Pie Chart</option>
                        <option value="doughnut">Doughnut Chart</option>
                    </select>
                    <select id="xAxisColumn" class="chart-columns-select">
                        {% for column in columns %}
                        <option value="{{ column }}">{{ column }}</option>
                        {% endfor %}
                    </select>
                    <select id="yAxisColumn" class="chart-columns-select">
                        {% for column in columns %}
                        <option value="{{ column }}" {% if loop.index == 2 %}selected{% endif %}>{{ column }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="chart-container">
                    <canvas id="dataChart"></canvas>
                </div>
            </div>
            <div class="table-container">
                <table id="resultsTable" class="table table-hover">
                    <thead>
                        <tr>
                            {% for column in columns %}
                            <th>{{ column }}</th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for row in data %}
                        <tr>
                            {% for value in row %}
                            <td>{{ value }}</td>
                            {% endfor %}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-warning" role="alert">
                No data available
            </div>
            {% endif %}

            {% if query %}
            <div class="query-section">
                <h5 class="mb-3">
                    <span class="toggle-btn" onclick="toggleQuery()">
                        <span id="toggleIcon">▼</span>
                        <span>Show Query</span>
                    </span>
                </h5>
                <div id="queryContent" class="query-content">
                    {{ query }}
                </div>
            </div>
            {% endif %}
        </div>

        <footer class="footer">
            <img src="{{ url_for('static', path='images/logo-distillery.png') }}" alt="Distillery Logo" class="footer-logo">
            <a href="https://www.distillery.com" class="footer-website" target="_blank">www.distillery.com</a>
            <div class="social-section">
                <span class="social-text">Follow us:</span>
                <div class="social-icons">
                    <a href="https://www.linkedin.com/company/Distillery-Tech/" class="social-icon" target="_blank">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <a href="https://www.facebook.com/DistilleryTech" class="social-icon" target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://www.instagram.com/Distillery_Tech/" class="social-icon" target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="https://twitter.com/DistilleryTech" class="social-icon" target="_blank">
                        <i class="fab fa-x-twitter"></i>
                    </a>
                </div>
            </div>
            <div class="copyright">© 2012-2025 Distillery. All rights reserved.</div>
        </footer>
    </div>

    <!-- jQuery (required for DataTables) -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <!-- Font Awesome for social icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- SheetJS for Excel export -->
    <script src="https://cdn.sheetjs.com/xlsx-0.20.1/package/dist/xlsx.full.min.js"></script>
    
    <!-- Prism.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-sql.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable
            const table = $('#resultsTable').DataTable({
                responsive: true,
                pageLength: 10,
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                order: [], // Disable initial sorting
                language: {
                    search: "Search:",
                    lengthMenu: "Show _MENU_ entries",
                    info: "Showing _START_ to _END_ of _TOTAL_ entries",
                    infoEmpty: "Showing 0 to 0 of 0 entries",
                    infoFiltered: "(filtered from _MAX_ total entries)",
                    zeroRecords: "No matching records found",
                    paginate: {
                        first: "First",
                        last: "Last",
                        next: "Next",
                        previous: "Previous"
                    }
                },
                dom: '<"table-controls"<"table-controls-left"f><"table-controls-right"<"export-container">>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"p>>' +
                     '<"row"<"col-sm-12"i>>',
                initComplete: function() {
                    // Add custom classes to DataTables elements
                    $('.dataTables_length select').addClass('form-select');
                    $('.dataTables_filter input').addClass('form-control');
                    
                    // Add export button with responsive text/icon
                    const exportButton = $('<button>', {
                        class: 'btn export-btn',
                        html: '<span class="export-text">Export to Excel </span><i class="export-icon fas fa-file-excel"></i>'
                    }).on('click', function() {
                        // Get the table data
                        const data = table.data().toArray();
                        const headers = table.columns().header().toArray().map(th => $(th).text());
                        
                        // Create worksheet
                        const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);
                        
                        // Create workbook
                        const wb = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(wb, ws, "Table Data");
                        
                        // Generate Excel file
                        XLSX.writeFile(wb, "table_export.xlsx");
                    });
                    
                    // Insert the button in the export container
                    $('.export-container').append(exportButton);
                }
            });
            
            // Chart.js initialization
            let chart = null;
            
            function createChart(chartType, xColumn, yColumn) {
                const ctx = document.getElementById('dataChart').getContext('2d');
                
                // Get data from the table using DataTables API
                const tableData = [];
                const visibleData = table.rows({ page: 'current' }).data();
                
                // Get column indices
                const xColumnIndex = table.column($(`th:contains('${xColumn}')`)).index();
                const yColumnIndex = table.column($(`th:contains('${yColumn}')`)).index();
                
                // Convert DataTables data to array of objects
                visibleData.each(function(rowData) {
                    const row = {};
                    table.columns().every(function() {
                        const columnName = $(this.header()).text();
                        row[columnName] = rowData[this.index()];
                    });
                    tableData.push(row);
                });

                const xData = tableData.map(row => row[xColumn]);
                const yData = tableData.map(row => {
                    const value = row[yColumn];
                    // Convert to number if possible
                    return isNaN(value) ? value : parseFloat(value);
                });
                
                // Destroy existing chart if it exists
                if (chart) {
                    chart.destroy();
                }
                
                // Create new chart
                chart = new Chart(ctx, {
                    type: chartType,
                    data: {
                        labels: xData,
                        datasets: [{
                            label: yColumn,
                            data: yData,
                            backgroundColor: 'rgba(0, 54, 189, 0.2)',
                            borderColor: 'rgba(0, 54, 189, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: `${yColumn} by ${xColumn}`
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }
            
            // Function to update chart with current selections
            function updateChart() {
                const chartType = $('#chartType').val();
                const xColumn = $('#xAxisColumn').val();
                const yColumn = $('#yAxisColumn').val();
                createChart(chartType, xColumn, yColumn);
            }
            
            // Event listeners for chart controls
            $('#chartType, #xAxisColumn, #yAxisColumn').on('change', updateChart);
            
            // Event listeners for table changes
            table.on('order.dt', updateChart);  // When sorting changes
            table.on('page.dt', updateChart);   // When page changes
            table.on('length.dt', updateChart); // When number of entries changes
            table.on('search.dt', updateChart); // When search/filter changes
            
            // Initialize chart with default values
            updateChart();
        });

        function toggleQuery() {
            const content = document.getElementById('queryContent');
            const icon = document.getElementById('toggleIcon');
            const button = document.querySelector('.toggle-btn span:last-child');
            
            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                icon.textContent = '▲';
                button.textContent = 'Hide Query';
                // Format and highlight the SQL query
                const query = content.textContent;
                const formattedQuery = formatSQL(query);
                content.innerHTML = `<pre><code class="language-sql">${formattedQuery}</code></pre>`;
                Prism.highlightElement(content.querySelector('code'));
            } else {
                content.style.display = 'none';
                icon.textContent = '▼';
                button.textContent = 'Show Query';
            }
        }

        function formatSQL(sql) {
            // Basic SQL formatting
            return sql
                .replace(/\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|HAVING|JOIN|LEFT JOIN|RIGHT JOIN|INNER JOIN|OUTER JOIN|ON|AND|OR|IN|NOT IN|EXISTS|NOT EXISTS|UNION|UNION ALL)\b/gi, '\n$1')
                .replace(/,/g, ',\n')
                .replace(/;/g, ';\n')
                .replace(/\n\s*\n/g, '\n') // Remove multiple empty lines
                .trim();
        }
    </script>
</body>
</html> 