from sqlalchemy.orm import Session
from app2.models.feedback import Feedback
from app2.schemas.feedback import FeedbackCreate


def create_feedback(db: Session, feedback: FeedbackCreate) -> Feedback:
    db_feedback = Feedback(
        message_ts=feedback.message_ts,
        channel_id=feedback.channel_id,
        user_id=feedback.user_id,
        chatbot_response=feedback.chatbot_response,
        user_question=feedback.user_question,
        s3_query_id=feedback.s3_query_id,
        reaction=feedback.reaction,
    )
    db.add(db_feedback)
    db.commit()
    db.refresh(db_feedback)
    return db_feedback
