from sqlalchemy import Column, Integer, String, Text, DateTime
from sqlalchemy.sql import func
from app2.db.base_class import Base


class MessageDeduplication(Base):
    """Model for tracking processed Slack messages to prevent duplicates."""

    __tablename__ = "message_deduplication"

    id = Column(Integer, primary_key=True, index=True)
    event_id = Column(String, nullable=False, unique=True)  # Slack event ID
    event_ts = Column(String, nullable=False)  # Slack event timestamp
    channel_id = Column(String, nullable=False)  # Slack channel ID
    user_id = Column(String, nullable=False)  # Slack user ID
    message_text = Column(Text, nullable=True)  # The message text
    processed_at = Column(DateTime(timezone=True), server_default=func.now())
    status = Column(
        String, nullable=False, default="processing"
    )  # processing, completed, failed
