from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from app2.db.base_class import Base


class Feedback(Base):
    __tablename__ = "feedback"

    id = Column(Integer, primary_key=True, index=True)
    message_ts = Column(String, unique=True, nullable=False)
    channel_id = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    chatbot_response = Column(Text, nullable=False)
    user_question = Column(
        Text, nullable=True
    )  # New column for storing the user's question
    s3_query_id = Column(String, nullable=True)
    reaction = Column(
        Boolean, nullable=False
    )  # True for thumbs up, False for thumbs down
    created_at = Column(DateTime(timezone=True), server_default=func.now())
