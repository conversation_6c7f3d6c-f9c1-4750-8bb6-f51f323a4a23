from pydantic import BaseModel
from typing import List, Literal, Dict, Any, Optional


class SlackAction(BaseModel):
    action_id: str
    type: Literal["button", "checkboxes"]
    value: Optional[str] = None
    selected_options: Optional[List[Dict[str, Any]]] = None


class SlackMessage(BaseModel):
    ts: str
    text: str
    blocks: List[Dict[str, Any]]


class SlackChannel(BaseModel):
    id: str


class SlackUser(BaseModel):
    id: str


class SlackInteractionPayload(BaseModel):
    type: Literal["block_actions"]
    actions: List[SlackAction]
    message: Optional[SlackMessage] = None
    channel: Optional[SlackChannel] = None
    user: SlackUser
