from datetime import datetime
from typing import Optional, Literal
from uuid import UUID
from pydantic import BaseModel, ConfigDict


class RecurringScheduleBase(BaseModel):
    message: str
    cron_expression: str
    timezone: str
    schedule_type: Literal["recurring", "one_time"] = "recurring"
    is_active: bool = True


class RecurringScheduleCreate(RecurringScheduleBase):
    user_id: str
    channel_id: str


class RecurringScheduleUpdate(BaseModel):
    message: Optional[str] = None
    cron_expression: Optional[str] = None
    timezone: Optional[str] = None
    schedule_type: Optional[Literal["recurring", "one_time"]] = None
    is_active: Optional[bool] = None


class RecurringScheduleInDB(RecurringScheduleBase):
    id: UUID
    user_id: str
    channel_id: str
    last_run: Optional[datetime] = None
    next_run: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    eventbridge_rule_arn: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)
