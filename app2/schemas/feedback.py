from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class FeedbackBase(BaseModel):
    message_ts: str
    channel_id: str
    user_id: str
    chatbot_response: str
    user_question: Optional[str] = None
    s3_query_id: Optional[str] = None
    reaction: bool


class FeedbackCreate(FeedbackBase):
    pass


class FeedbackInDB(FeedbackBase):
    id: int
    created_at: datetime

    class Config:
        from_attributes = True
