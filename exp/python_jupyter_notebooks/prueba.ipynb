{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "import ssl\n", "\n", "import certifi\n", "from slack_sdk import WebClient\n", "from slack_sdk.errors import SlackApiError\n", "\n", "from app2.config import Settings\n", "\n", "# Create a Settings instance\n", "app_config = Settings()\n", "\n", "ssl_context = ssl.create_default_context(cafile=certifi.where())\n", "client = WebClient(token=app_config.slack_bot_token, ssl=ssl_context)\n", "\n", "\n", "def get_last_bot_conversations(channel_id: str, limit: int = 3) -> list:\n", "    try:\n", "        response = client.conversations_history(channel=channel_id, limit=limit * 2)\n", "        messages = response.get(\"messages\", [])[::-1]\n", "        return messages\n", "\n", "    except SlackApiError as e:\n", "        logging.error(f\"Error retrieving messages: {e.response['error']}\")\n", "        return []"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'user': 'U08DKQG1XM3',\n", "  'type': 'message',\n", "  'ts': '1741645822.104369',\n", "  'client_msg_id': '3d86b429-9961-4192-80b6-bcd34487cf10',\n", "  'text': 'y de que colores existen las Macs?? la respuesta lo mas corto posible',\n", "  'team': 'T03G61VPV',\n", "  'blocks': [{'type': 'rich_text',\n", "    'block_id': 'PVLJn',\n", "    'elements': [{'type': 'rich_text_section',\n", "      'elements': [{'type': 'text',\n", "        'text': 'y de que colores existen las Macs?? la respuesta lo mas corto posible'}]}]}]},\n", " {'user': 'U08H1VC72MN',\n", "  'type': 'message',\n", "  'ts': '1741645824.436919',\n", "  'bot_id': 'B08H1VC6Z8Q',\n", "  'app_id': 'A08G5BNFKBQ',\n", "  'text': '<PERSON><PERSON> <@U08DKQG1XM3>, Las Macs están disponibles en colores como plata, gris espacial, dorado y rosa.',\n", "  'team': 'T03G61VPV',\n", "  'bot_profile': {'id': 'B08H1VC6Z8Q',\n", "   'app_id': 'A08G5BNFKBQ',\n", "   'name': 'slack_genie_bot',\n", "   'icons': {'image_36': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_36.jpg',\n", "    'image_48': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_48.jpg',\n", "    'image_72': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_72.jpg'},\n", "   'deleted': <PERSON><PERSON><PERSON>,\n", "   'updated': 1741234225,\n", "   'team_id': 'T03G61VPV'},\n", "  'blocks': [{'type': 'rich_text',\n", "    'block_id': 'BiU',\n", "    'elements': [{'type': 'rich_text_section',\n", "      'elements': [{'type': 'text', 'text': 'Hola '},\n", "       {'type': 'user', 'user_id': 'U08DKQG1XM3'},\n", "       {'type': 'text',\n", "        'text': ', Las Macs están disponibles en colores como plata, gris espacial, dorado y rosa.'}]}]}]},\n", " {'user': 'U08DKQG1XM3',\n", "  'type': 'message',\n", "  'ts': '1741645981.790179',\n", "  'client_msg_id': 'e1ed4de2-8b07-4ca2-8ccb-fbc3147e087a',\n", "  'text': 'y que estudia la ciencia geografica? en menos de 10 palabras',\n", "  'team': 'T03G61VPV',\n", "  'blocks': [{'type': 'rich_text',\n", "    'block_id': 'tgsi+',\n", "    'elements': [{'type': 'rich_text_section',\n", "      'elements': [{'type': 'text',\n", "        'text': 'y que estudia la ciencia geografica? en menos de 10 palabras'}]}]}]},\n", " {'user': 'U08H1VC72MN',\n", "  'type': 'message',\n", "  'ts': '1741645984.243959',\n", "  'bot_id': 'B08H1VC6Z8Q',\n", "  'app_id': 'A08G5BNFKBQ',\n", "  'text': '<PERSON><PERSON> <@U08DKQG1XM3>, Estudia la Tierra, sus características y relaciones humanas.',\n", "  'team': 'T03G61VPV',\n", "  'bot_profile': {'id': 'B08H1VC6Z8Q',\n", "   'app_id': 'A08G5BNFKBQ',\n", "   'name': 'slack_genie_bot',\n", "   'icons': {'image_36': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_36.jpg',\n", "    'image_48': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_48.jpg',\n", "    'image_72': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_72.jpg'},\n", "   'deleted': <PERSON><PERSON><PERSON>,\n", "   'updated': 1741234225,\n", "   'team_id': 'T03G61VPV'},\n", "  'blocks': [{'type': 'rich_text',\n", "    'block_id': 'oKoF',\n", "    'elements': [{'type': 'rich_text_section',\n", "      'elements': [{'type': 'text', 'text': 'Hola '},\n", "       {'type': 'user', 'user_id': 'U08DKQG1XM3'},\n", "       {'type': 'text',\n", "        'text': ', Estudia la Tierra, sus características y relaciones humanas.'}]}]}]},\n", " {'user': 'U08DKQG1XM3',\n", "  'type': 'message',\n", "  'ts': '1741646115.860229',\n", "  'client_msg_id': 'ac19fce7-a273-4ed3-afe1-026a4b6f565e',\n", "  'text': 'y que estudia la fisica? en menos de 10 apalabras',\n", "  'team': 'T03G61VPV',\n", "  'blocks': [{'type': 'rich_text',\n", "    'block_id': 'CSfU9',\n", "    'elements': [{'type': 'rich_text_section',\n", "      'elements': [{'type': 'text',\n", "        'text': 'y que estudia la fisica? en menos de 10 apalabras'}]}]}]},\n", " {'user': 'U08H1VC72MN',\n", "  'type': 'message',\n", "  'ts': '1741646118.425309',\n", "  'bot_id': 'B08H1VC6Z8Q',\n", "  'app_id': 'A08G5BNFKBQ',\n", "  'text': '<PERSON><PERSON> <@U08DKQG1XM3>, La física estudia la materia, energía y sus interacciones.',\n", "  'team': 'T03G61VPV',\n", "  'bot_profile': {'id': 'B08H1VC6Z8Q',\n", "   'deleted': <PERSON><PERSON><PERSON>,\n", "   'name': 'slack_genie_bot',\n", "   'updated': 1741234225,\n", "   'app_id': 'A08G5BNFKBQ',\n", "   'icons': {'image_36': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_36.jpg',\n", "    'image_48': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_48.jpg',\n", "    'image_72': 'https://avatars.slack-edge.com/2025-03-05/8560779091956_c9600baa29984b2b8ffa_72.jpg'},\n", "   'team_id': 'T03G61VPV'},\n", "  'blocks': [{'type': 'rich_text',\n", "    'block_id': '+yxB8',\n", "    'elements': [{'type': 'rich_text_section',\n", "      'elements': [{'type': 'text', 'text': 'Hola '},\n", "       {'type': 'user', 'user_id': 'U08DKQG1XM3'},\n", "       {'type': 'text',\n", "        'text': ', La física estudia la materia, energía y sus interacciones.'}]}]}]}]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["messages = get_last_bot_conversations(channel_id=\"D08H2H95X5E\", limit=3)\n", "messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["- user_message: y de que colores existen las Macs?? la respuesta lo mas corto posible\n", "- assitant_message: <PERSON><PERSON> <@U08DKQG1XM3>, Las Macs están disponibles en colores como plata, gris espacial, dorado y rosa.\n", "- user_message: y que estudia la ciencia geografica? en menos de 10 palabras\n", "- assitant_message: <PERSON><PERSON> <@U08DKQG1XM3>, Estudia la Tierra, sus características y relaciones humanas.\n", "- user_message: y que estudia la fisica? en menos de 10 apalabras\n", "- assitant_message: <PERSON><PERSON> <@U08DKQG1XM3>, La física estudia la materia, energía y sus interacciones.\n"]}], "source": ["def format_chat_history(messages: list) -> str:\n", "    \"\"\"\n", "    Formats the chat history into the desired structure.\n", "\n", "    Args:\n", "        messages (list): List of dictionaries containing user and bot messages.\n", "\n", "    Returns:\n", "        str: Formatted chat history in key-value structure.\n", "    \"\"\"\n", "\n", "    if len(messages) % 2 != 0:\n", "        raise ValueError(\n", "            \"La lista de mensajes debe contener pares de usuario y asistente.\"\n", "        )\n", "\n", "    formatted_output = \"\\n\".join(\n", "        f\"- user_message: {messages[index].get('text')}\\n- assitant_message: {messages[index+1].get('text')}\"\n", "        for index in range(0, len(messages), 2)\n", "    )\n", "\n", "    return formatted_output\n", "\n", "\n", "print(format_chat_history(messages))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}