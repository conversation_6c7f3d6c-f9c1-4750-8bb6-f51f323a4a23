{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/pinecone-io/examples/blob/master/learn/generation/chatbots/chatbot.ipynb) [![Open nbviewer](https://raw.githubusercontent.com/pinecone-io/examples/master/assets/nbviewer-shield.svg)](https://nbviewer.org/github/pinecone-io/examples/blob/master/learn/generation/chatbots/chatbot.ipynb)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Chatbots\n", "\n", "The most powerful chatbots in the world all **hallucinate** and lack an up-to-date understanding of the real world. GPT-4, for example, cannot answer questions about recent world events, no matter how important they were.\n", "\n", "The world of chatbots is frozen in time. Their world exists as a static snapshot of the world as it was within their training data.\n", "\n", "A solution to this problem is _retrieval augmentation_. The idea behind this is plug-in a chatbot to an external vector database where they can then access _accurate_ and up-to-date and information. Helping us limit hallucinations and answer questions about the latest events. In this example we will see how to do that.\n", "\n", "To begin, we must install the prerequisite libraries that we will be using in this notebook.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install -qU \\\n", "  langchain==0.0.162 \\\n", "  openai==0.27.7 \\\n", "  tiktoken==0.4.0 \\\n", "  \"pinecone-client[grpc]\"==2.2.1 \\\n", "  pinecone-datasets=='0.5.0rc11'"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["---\n", "🚨 _Note: the above `pip install` is formatted for Jupyter notebooks. If running elsewhere you may need to drop the `!`._\n", "---\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Data Preparation\n", "\n", "The chatbot use-case requires that we store relevant information (called **contexts**) inside the vector database. To make these retrievable we encode them to create vector embeddings. Once these embeddings are indexed we can use them to augment user queries as demonstrated below:\n", "\n", "![Chatbot with retrieval augmentation diagram](https://github.com/pinecone-io/examples/blob/master/learn/generation/chatbots/assets/chatbot-diagram.png?raw=true)\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Downloading the Dataset\n", "\n", "We will download a pre-embedding dataset from `pinecone-datasets`. Allowing us to skip the embedding and any other preprocessing steps. When working with your own dataset you will need to perform this embedding step but we have prebuilt the embeddings so we can jump right to the action.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.local/share/mise/installs/python/3.11.9/lib/python3.11/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import pinecone_datasets\n", "\n", "dataset = pinecone_datasets.load_dataset(\"wikipedia-simple-text-embedding-ada-002\")\n", "dataset.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["283945"]}, "metadata": {}, "output_type": "display_data"}], "source": ["len(dataset)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["A `pinecone-dataset` always contains `id`, `values`, `sparse_values`, `metadata`, and `blob`. All we need are the IDs, vector embeddings (stored in `values`), and some metadata (which is actually stored in `blob`). Let's reformat the dataset ready for adding to Pinecone, and use a smaller subset of the full dataset.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["30000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# we drop sparse_values as they are not needed for this example\n", "dataset.documents.drop([\"sparse_values\", \"metadata\"], axis=1, inplace=True)\n", "dataset.documents.rename(columns={\"blob\": \"metadata\"}, inplace=True)\n", "# we will use rows of the dataset up to index 30_000\n", "dataset.documents.drop(dataset.documents.index[30_000:], inplace=True)\n", "len(dataset)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now we move on to initializing our Pinecone vector database.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initializing the Index\n", "\n", "Now we need a place to store these embeddings and enable a efficient vector search through them all. To do that we use Pinecone, we can get a [free API key](https://app.pinecone.io/) and enter it below where we will initialize our connection to Pinecone and create a new index.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from pinecone import Pinecone\n", "\n", "# initialize connection to pinecone (get API key at app.pinecone.io)\n", "api_key = os.environ.get(\"PINECONE_API_KEY\") or \"PINECONE_API_KEY\"\n", "\n", "# configure client\n", "pc = Pinecone(api_key=api_key)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we setup our index specification, this allows us to define the cloud provider and region where we want to deploy our index. You can find a list of all [available providers and regions here](https://docs.pinecone.io/docs/projects).\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from pinecone import ServerlessSpec\n", "\n", "cloud = os.environ.get(\"PINECONE_CLOUD\") or \"aws\"\n", "region = os.environ.get(\"PINECONE_REGION\") or \"us-east-1\"\n", "\n", "spec = ServerlessSpec(cloud=cloud, region=region)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["index_name = \"chatbot-onboarding\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "# check if index already exists (it shouldn't if this is first time)\n", "if index_name not in pc.list_indexes().names():\n", "    # if does not exist, create index\n", "    pc.create_index(\n", "        index_name,\n", "        dimension=1536,  # dimensionality of text-embedding-ada-002\n", "        metric=\"cosine\",\n", "        spec=spec,\n", "    )\n", "    # wait for index to be initialized\n", "    while not pc.describe_index(index_name).status[\"ready\"]:\n", "        time.sleep(1)\n", "\n", "# connect to index\n", "index = pc.Index(index_name)\n", "# view index stats\n", "index.describe_index_stats()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We should see that the new Pinecone index has a `total_vector_count` of `0`, as we haven't added any vectors yet.\n", "\n", "Now we upsert the data to Pinecone:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "47188360f07a4c018bf7d2c5eb6f626c", "version_major": 2, "version_minor": 0}, "text/plain": ["sending upsert requests:   0%|          | 0/30000 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "75cef31e615d4851b029c3aacacf9c2d", "version_major": 2, "version_minor": 0}, "text/plain": ["collecting async responses:   0%|          | 0/300 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["upserted_count: 30000"]}, "metadata": {}, "output_type": "display_data"}], "source": ["index.upsert_from_dataframe(dataset.documents, batch_size=100)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We've now indexed everything. We can check the number of vectors in our index like so:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'dimension': 1536,\n", " 'index_fullness': 0.1,\n", " 'namespaces': {'': {'vector_count': 30000}},\n", " 'total_vector_count': 30000}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["index.describe_index_stats()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Building the Chatbot and Querying\n", "\n", "Now that we've build our index we can switch over to LangChain. We need to initialize a LangChain vector store using the same index we just built. For this we will also need a LangChain embedding object, which we initialize like so:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.embeddings.openai import OpenAIEmbeddings\n", "\n", "# get openai api key from platform.openai.com\n", "OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\") or \"OPENAI_API_KEY\"\n", "\n", "model_name = \"text-embedding-ada-002\"\n", "\n", "embed = OpenAIEmbeddings(model=model_name, openai_api_key=OPENAI_API_KEY)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now initialize the vector store:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.vectorstores import Pinecone\n", "\n", "text_field = \"text\"\n", "\n", "# switch back to normal index for langchain\n", "index = pc.Index(index_name)\n", "\n", "vectorstore = Pinecone(index, embed.embed_query, text_field)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now we can query the vector store directly using `vectorstore.similarity_search`:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(page_content='<PERSON>OM GCTE (29 July 1883 – 28 April 1945) was an Italian politician and journalist. He was also the Prime Minister of Italy from 1922 until 1943. He was the leader of the National Fascist Party.\\n\\nBiography\\n\\nEarly life\\nB<PERSON>to <PERSON> was named after <PERSON>, a Mexican opponent of the political power of the Roman Catholic Church, by his anticlerical (a person who opposes the political interference of the Roman Catholic Church in secular affairs) father. <PERSON>\\'s father was a blacksmith. Before being involved in politics, <PERSON> was a newspaper editor (where he learned all his propaganda skills) and elementary school teacher.\\n\\nAt first, <PERSON> was a socialist, but when he wanted Italy to join the First World War, he was thrown out of the socialist party. He \\'invented\\' a new ideology, Fascism, much out of Nationalist\\xa0and Conservative views.\\n\\nRise to power and becoming dictator\\nIn 1922, he took power by having a large group of men, \"Black Shirts,\" march on Rome and threaten to take over the government. King <PERSON><PERSON><PERSON> gave in, allowed him to form a government, and made him prime minister. In the following five years, he gained power, and in 1927 created the OVRA, his personal secret police force. Using the agency to arrest, scare, or murder people against his regime, <PERSON> was dictator\\xa0of Italy by the end of 1927. Only the King and his own Fascist party could challenge his power.', metadata={'chunk': 0.0, 'source': 'https://simple.wikipedia.org/wiki/<PERSON>%20<PERSON>ussolini', 'title': '<PERSON> <PERSON>', 'wiki-id': '6754'}),\n", " Document(page_content='Fascism as practiced by <PERSON>\\nMussolini\\'s form of Fascism, \"Italian Fascism\"- unlike Nazism, the racist ideology that <PERSON> followed- was different and less destructive than <PERSON>\\'s. Although a believer in the superiority of the Italian nation and national unity, <PERSON>, unlike <PERSON>, is quoted \"Race? It is a feeling, not a reality. Nothing will ever make me believe that biologically pure races can be shown to exist today\".\\n\\n<PERSON><PERSON><PERSON><PERSON> wanted Italy to become a new Roman Empire. In 1923, he attacked the island of Corfu, and in 1924, he occupied the city state of Fiume. In 1935, he attacked the African country Abyssinia (now called Ethiopia). His forces occupied it in 1936. Italy was thrown out of the League of Nations because of this aggression. In 1939, he occupied the country Albania. In 1936, <PERSON> signed an alliance with <PERSON>, the dictator of Germany.\\n\\nFall from power and death\\nIn 1940, he sent Italy into the Second World War on the side of the Axis countries. <PERSON> attacked Greece, but he failed to conquer it. In 1943, the Allies landed in Southern Italy. The Fascist party and King <PERSON><PERSON><PERSON> deposed <PERSON> and put him in jail, but he was set free by the Germans, who made him ruler of the Italian Social Republic puppet state which was in a small part of Central Italy. When the war was almost over, <PERSON> tried to escape to Switzerland with his mistress, <PERSON>, but they were both captured and shot by partisans. <PERSON>\\'s dead body was hanged upside-down, together with his mistress and some of <PERSON>\\'s helpers, on a pole at a gas station in the village of Millan, which is near the border  between Italy and Switzerland.', metadata={'chunk': 1.0, 'source': 'https://simple.wikipedia.org/wiki/Benito%20Mussolini', 'title': 'Benito Mussolini', 'wiki-id': '6754'}),\n", " Document(page_content='<PERSON>ene<PERSON> was made part of Italy in 1866 after a war with Austria. Italian soldiers won Latium in 1870. That was when they took away the Pope\\'s power. The Pope, who was angry, said that he was a prisoner to keep Catholic people from being active in politics. That was the year of Italian unification.\\n\\nItaly participated in World War I. It was an ally of Great Britain, France, and Russia against the Central Powers. Almost all of Italy\\'s fighting was on the Eastern border, near Austria. After the \"Caporetto defeat\", Italy thought they would lose the war. But, in 1918, the Central Powers surrendered. Italy gained the Trentino-South Tyrol, which once was owned by Austria.\\n\\nFascist Italy \\nIn 1922, a new Italian government started. It was ruled by <PERSON>, the leader of Fascism in Italy. He became head of government and dictator, calling himself \"<PERSON>\" (which means \"leader\" in Italian). He became friends with German dictator <PERSON>. Germany, Japan, and Italy became the Axis Powers. In 1940, they entered World War II together against France, Great Britain, and later the Soviet Union. During the war, Italy controlled most of the Mediterranean Sea.', metadata={'chunk': 5.0, 'source': 'https://simple.wikipedia.org/wiki/Italy', 'title': 'Italy', 'wiki-id': '363'})]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["query = \"who was <PERSON>?\"\n", "\n", "vectorstore.similarity_search(\n", "    query,  # our search query\n", "    k=3,  # return 3 most relevant docs\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["All of these are good, relevant results. But this actually covers only the first few steps of our retrieval augmented chatbot, the **retrieval augmentation** steps. We're still missing the **chatbot** part.\n", "\n", "### Creating the Chatbot\n", "\n", "Our chatbot will take the query as a question that is to be answered, but the chatbot must answer the question based on the information it is seeing being returned from the `vectorstore`.\n", "\n", "To do this we initialize a chat model and `RetrievalQA` object like so:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.chat_models import ChatOpenAI\n", "from langchain.chains import RetrievalQA\n", "\n", "# chatbot language model\n", "llm = ChatOpenAI(\n", "    openai_api_key=OPENAI_API_KEY, model_name=\"gpt-3.5-turbo\", temperature=0.0\n", ")\n", "# retrieval augmented pipeline for chatbot\n", "qa = RetrievalQA.from_chain_type(\n", "    llm=llm, chain_type=\"stuff\", retriever=vectorstore.as_retriever()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"type": "string"}, "text/plain": ["'<PERSON> was an Italian politician and journalist who served as the Prime Minister of Italy from 1922 until 1943. He was the leader of the National Fascist Party and was also a newspaper editor and elementary school teacher before being involved in politics. <PERSON> was a dictator of Italy by the end of 1927, and his form of Fascism, \"Italian Fascism,\" was different and less destructive than <PERSON>\\'s Nazism. He wanted Italy to become a new Roman Empire and attacked several countries, including Abyssinia (now called Ethiopia) and Greece. <PERSON> was captured and shot by partisans in 1945.'"]}, "metadata": {}, "output_type": "display_data"}], "source": ["qa.run(query)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can also include the sources of information that the chatbot is using to answer our question. We can do this using a slightly different version of `RetrievalQA` called `RetrievalQAWithSourcesChain`:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from langchain.chains import RetrievalQAWithSourcesChain\n", "\n", "qa_with_sources = RetrievalQAWithSourcesChain.from_chain_type(\n", "    llm=llm, chain_type=\"stuff\", retriever=vectorstore.as_retriever()\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'question': 'who was <PERSON>?',\n", " 'answer': '<PERSON> was an Italian politician and journalist who was the Prime Minister of Italy from 1922 until 1943. He was the leader of the National Fascist Party and created his own form of Fascism called \"Italian Fascism\". He wanted Italy to become a new Roman Empire and attacked several countries, including Abyssinia (now called Ethiopia) and Greece. He was dictator of Italy by the end of 1927 and was deposed in 1943. He was later captured and shot by partisans. <PERSON> is the current head of government of Italy. Italy was not a state before 1861 and was a group of separate states ruled by other countries. In 1860, <PERSON> took control of Sicily, creating the Kingdom of Italy in 1861. <PERSON> was made the king. \\n',\n", " 'sources': 'https://simple.wikipedia.org/wiki/Benito%20M<PERSON><PERSON>, https://simple.wikipedia.org/wiki/Italy'}"]}, "metadata": {}, "output_type": "display_data"}], "source": ["qa_with_sources(query)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now we answer the question being asked, _and_ return the source of this information being used by the LLM.\n", "\n", "Once done, we can delete the index to save resources.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pc.delete_index(index_name)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Where would we use this?\n", "\n", "Chatbots and retrieval augmented LLMs are incredibly prelevant in our world despite their lack of adoption pre-ChatGPT. Retrieval augmentation is being used to improve the performance and reliability of chatbots and the retrieval _with sources_ that we demonstrated above is clearly visible in both Google's Bard and Microsoft's Bing AI. Beyond these tech giants, giving LLMs access to up-to-date information is essential for customer service chatbots that must refer to customer data and FAQ docs, for chatbots plugged into fast changing ecommerce databases, and any scenario where up-to-date information is important or helpful.\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["---\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}