[build-system]
requires = [ "poetry-core",]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
package-mode = true
name = "slackgenie"
version = "0.10.6"
description = ""
authors = [ "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <axel.von<PERSON>@distillery.com>",]
readme = "README.md"
[[tool.poetry.packages]]
include = "app2"

[tool.poetry.dependencies]
python = "^3.12"
langchain = "^0.2.12"
langchain-openai = "^0.1.20"
langchain-core = "^0.2.32"
sqlalchemy = "^2.0.35"
alembic = "^1.13.3"
pydantic-settings = "^2.5.2"
slack-bolt = "^1.22.0"
langgraph = "^0.3.16"
mangum = "^0.19.0"
boto3 = "^1.37.23"
pandas = "^2.2.3"
prettytable = "^3.16.0"
numpy = ">=1.26.0,<2.0.0"
openpyxl = "^3.1.5"
psycopg2-binary = "^2.9.10"
croniter = "^6.0.0"
pytz = "^2025.2"
h11 = "^0.16.0"
python-multipart = "^0.0.18"
starlette = "^0.40.0"
Jinja2 = "^3.1.6"
requests = "^2.32.4"
aiohttp = "^3.10.11"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"

[tool.pytest.ini_options]
python_classes = [ "Test", "Describe",]
python_functions = [ "test_", "it_", "and_", "but_", "they_",]
python_files = [ "test_*.py",]
testpaths = [ "tests",]

[tool.poetry.dependencies.fastapi]
extras = [ "standard",]
version = "^0.115.0"

[tool.poetry.dependencies.uvicorn]
extras = [ "standard",]
version = "^0.30.5"

[tool.poetry.group.oldapp]
optional = true

[tool.poetry.group.test]
optional = true

[tool.poetry.group.web]
optional = true

[tool.poetry.group.docs]
optional = true

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.infra]
optional = true

[tool.poetry.group.jupyter]
optional = true

[tool.poetry.group.test.dependencies]
python = "^3.12"
pytest = "^8.3.4"
pytest-mock = "*"

[tool.poetry.group.web.dependencies]
python = "^3.12"
python-fasthtml = "^0.3.4"

[tool.poetry.group.dev.dependencies]
python = "^3.12"
gradio-client = "^1.2.0"
deptry = "^0.19.1"
poetry-plugin-export = "^1.8.0"
pytest = "^8.3.4"
appmap = "^2.1.8"

[tool.poetry.group.docs.dependencies]
mkdocs-material = "^9.6.9"
mkdocs-git-revision-date-localized-plugin = "^1.4.5"
mkdocs-awesome-pages-plugin = "^2.10.1"
mkdocs-include-markdown-plugin = "^7.1.5"
diagrams = "^0.24.4"

[tool.poetry.group.infra.dependencies]
python = "^3.12"
pulumi = "^3.129.0"
pulumi-docker-build = "^0.0.5"
pulumi-gcp = "^7.35.0"
pulumi-docker = "^4.5.5"
pulumi-kubernetes = "^4.16.0"
pulumiverse-time = "^0.0.17"

[tool.poetry.group.jupyter.dependencies]
slack_sdk = "^3.35.0"

[tool.poetry.group.oldapp.dependencies]
python = "^3.12"
authlib = "^1.3.1"
docx2txt = "^0.8"
google-api-python-client = "^2.141.0"
gradio = "^4.36.1"
langchain-community = "^0.2.10"
langchain-experimental = "^0.0.64"
pinecone = "^5.0.0"
load-dotenv = "^0.1.0"
networkx = "^3.3"
nltk = "3.8.1"
openpyxl = "^3.1.5"
pandas = "^2.2.2"
psycopg2-binary = "^2.9.9"
pydantic-settings = "^2.5.2"
pypdf = "^4.3.1"
rapidfuzz = "^3.9.7"
tabulate = "^0.9.0"

[tool.poetry.group.oldapp.dependencies.langchain_pinecone]
version = "^0.1.3"
python = ">=3.11,<3.13"
