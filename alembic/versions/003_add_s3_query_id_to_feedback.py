"""add s3_query_id to feedback table

Revision ID: 003
Revises: 002
Create Date: 2024-03-21 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None

def upgrade() -> None:
    op.add_column('feedback', sa.Column('s3_query_id', sa.String(), nullable=True))

def downgrade() -> None:
    op.drop_column('feedback', 's3_query_id') 