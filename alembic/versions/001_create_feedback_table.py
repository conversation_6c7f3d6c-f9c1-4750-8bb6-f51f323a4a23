"""create feedback table

Revision ID: 001
Revises: 
Create Date: 2024-03-21 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None

def upgrade() -> None:
    op.create_table(
        'feedback',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('message_ts', sa.String(), nullable=False),
        sa.<PERSON>umn('channel_id', sa.String(), nullable=False),
        sa.<PERSON>umn('user_id', sa.String(), nullable=False),
        sa.Column('chatbot_response', sa.Text(), nullable=False),
        sa.Column('reaction', sa.<PERSON>(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()')),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('message_ts')
    )

def downgrade() -> None:
    op.drop_table('feedback') 