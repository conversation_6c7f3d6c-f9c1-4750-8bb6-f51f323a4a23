"""add show table results to user preferences

Revision ID: edac0ceb45f2
Revises: ab49d37c2e5f
Create Date: 2024-05-14 18:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'edac0ceb45f2'
down_revision = 'ab49d37c2e5f'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add show_table_results column if it doesn't exist
    op.add_column('user_preferences',
        sa.Column('show_table_results', sa.<PERSON>(), nullable=True, server_default='true')
    )


def downgrade() -> None:
    # Remove the column in case of rollback
    op.drop_column('user_preferences', 'show_table_results') 