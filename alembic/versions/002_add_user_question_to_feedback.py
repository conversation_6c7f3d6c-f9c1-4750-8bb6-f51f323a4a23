"""add user_question to feedback table

Revision ID: 002
Revises: 001
Create Date: 2024-03-21 11:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None

def upgrade() -> None:
    op.add_column('feedback', sa.Column('user_question', sa.Text(), nullable=True))

def downgrade() -> None:
    op.drop_column('feedback', 'user_question') 