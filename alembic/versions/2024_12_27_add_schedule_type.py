"""add schedule_type column to recurring_schedules

Revision ID: 20241227_schedule_type
Revises: 004
Create Date: 2024-12-27 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '20241227_schedule_type'
down_revision = '004'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add the schedule_type column with default value 'recurring'
    op.add_column(
        'recurring_schedules',
        sa.Column('schedule_type', sa.String(), nullable=False, server_default='recurring')
    )


def downgrade() -> None:
    # Remove the schedule_type column
    op.drop_column('recurring_schedules', 'schedule_type') 