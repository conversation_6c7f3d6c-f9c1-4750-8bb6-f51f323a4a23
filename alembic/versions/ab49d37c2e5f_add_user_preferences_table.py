"""add user preferences table

Revision ID: ab49d37c2e5f
Revises: 003
Create Date: 2025-05-14 18:09:26.911481

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ab49d37c2e5f'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_preferences',
    sa.<PERSON>umn('user_id', sa.String(), nullable=False),
    sa.<PERSON>umn('show_feedback', sa.<PERSON>(), nullable=True),
    sa.<PERSON>umn('show_detailed_results', sa.<PERSON>(), nullable=True),
    sa.<PERSON>umn('show_sql_query', sa.<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('user_id')
    )
    op.create_index(op.f('ix_user_preferences_user_id'), 'user_preferences', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    op.drop_table('user_preferences') 