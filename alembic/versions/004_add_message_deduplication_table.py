"""add_message_deduplication_table

Revision ID: 004
Revises: 20240515_rs
Create Date: 2025-05-26 16:14:32.250709

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '20240515_rs'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create message_deduplication table
    op.create_table(
        'message_deduplication',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('event_id', sa.String(), nullable=False),
        sa.Column('event_ts', sa.String(), nullable=False),
        sa.Column('channel_id', sa.String(), nullable=False),
        sa.Column('user_id', sa.String(), nullable=False),
        sa.Column('message_text', sa.Text(), nullable=True),
        sa.Column('processed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('status', sa.String(), nullable=False, server_default='processing'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('event_id')
    )


def downgrade() -> None:
    # Drop message_deduplication table
    op.drop_table('message_deduplication') 