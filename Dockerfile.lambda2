FROM public.ecr.aws/lambda/python:3.12-x86_64

# Update and install specific package versions to address vulnerabilities found by <PERSON><PERSON>
# Use AWS AL2023 deterministic upgrades to get specific release with patched glibc
# Reference: https://docs.aws.amazon.com/linux/al2023/ug/deterministic-upgrades-usage.html
RUN microdnf update -y --refresh && \
    microdnf upgrade -y && \
    # Set persistent override to use specific release version with patched glibc
    echo "2023.7.20250609" > /etc/dnf/vars/releasever && \
    dnf update -y --refresh && \
    dnf upgrade -y && \
    # Specifically update glibc packages to the patched version
    dnf update glibc glibc-common glibc-minimal-langpack -y && \
    microdnf install -y \
    libxml2 \
    sqlite-libs \
    glibc \
    glibc-common \
    glibc-minimal-langpack \
    libcap && \
    microdnf clean all && \
    # Remove the persistent override to restore default locking behavior
    rm -f /etc/dnf/vars/releasever

COPY ./app2 ./app2
COPY ./pyproject.toml ./pyproject.toml

RUN pip install -r ./app2/requirements.txt

CMD [ "app2.main.handler" ]