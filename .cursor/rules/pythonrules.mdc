---
description: 
globs: 
alwaysApply: true
---
You are an expert in Python, FastAPI, LangGraph, PostgreSQL, and scalable multi-tenant API systems. You are working on a project that connects a Slack chatbot to an LLM orchestration system using LangGraph. The backend is built with FastAPI, follows a modular architecture, and communicates through Slack as the main interface.

General Code Style and Structure

- Write concise, modular Python code using `async def` where applicable.
- Favor functional programming and early returns over deeply nested logic.
- Organize project by responsibility: `routers/`, `schemas/`, `services/`, `dependencies/`, `db/`, `models/`.

FastAPI + Python Guidelines

- Use `async def` for all I/O-bound operations and `def` for pure logic.
- Use Pydantic v2 models for input/output schemas. Prefer interfaces using BaseModel.
- Use dependency injection for DB sessions and shared resources.
- Handle errors with HTTPException and middleware. Avoid deeply nested conditionals.
- Use lifespan context managers instead of `@app.on_event`.

FastAPI-Specific Guidelines

- Use functional components (plain functions) and Pydantic models for input validation and response schemas.
- Use declarative route definitions with clear return type annotations.
- Use `def` for synchronous operations and `async def` for asynchronous ones.
- Minimize `@app.on_event("startup")` and `@app.on_event("shutdown")`; prefer lifespan context managers.
- Use middleware for logging, error monitoring, and performance optimization.
- Optimize for performance using async functions for I/O-bound tasks, caching strategies, and lazy loading.
- Use HTTPException for expected errors and model them as specific HTTP responses.
- Use middleware for handling unexpected errors, logging, and error monitoring.
- Use Pydantic's BaseModel for consistent input/output validation and response schemas.

Performance Optimization

- Minimize blocking I/O operations; use asynchronous operations for all database calls and external API requests.
- Implement caching for static and frequently accessed data using tools like Redis or in-memory stores.
- Optimize data serialization and deserialization with Pydantic.
- Use lazy loading techniques for large datasets and substantial API responses.

Key Conventions

1. Rely on FastAPI’s dependency injection system for managing state and shared resources.
2. Prioritize API performance metrics (response time, latency, throughput).
3. Limit blocking operations in routes:
   - Favor asynchronous and non-blocking flows.
   - Use dedicated async functions for database and external API operations.
   - Structure routes and dependencies clearly to optimize readability and maintainability.

LangGraph + Prompt Routing

- Use LangGraph to manage agent state, routing, and tool orchestration.
- Separate graph nodes for user input, memory updates, tool calls (e.g., Slack API), and LLM calls.
- Store triplets and conversation history in memory for accurate follow-ups.
- Keep prompts modular and versioned in code.

Slack Integration

- Receive events from Slack using a verified app.
- Use Slack bot tokens and APIs to post messages and handle interactions.
- Maintain session context for each conversation to support follow-ups.
