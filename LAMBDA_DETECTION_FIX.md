# Lambda Environment Detection Fix

## Problem
The `/schedule_msg` command was incorrectly detecting the environment when deployed in AWS Lambda, thinking it was running in local development instead of the Lambda environment. This caused the command to use the wrong handler and configuration.

## Root Cause
The original Lambda detection method relied solely on the `AWS_EXECUTION_ENV` environment variable:

```python
is_lambda = os.environ.get("AWS_EXECUTION_ENV", "").startswith("AWS_Lambda_")
```

However, this environment variable:
- May not be set in all Lambda runtime versions
- Can have different values depending on the Lambda runtime
- Is not always reliable across different AWS Lambda configurations

## Solution
Implemented a robust Lambda detection method that checks multiple environment variables that are consistently present in AWS Lambda environments:

### New Detection Method
```python
def is_running_in_lambda():
    """
    Detect if we're running in AWS Lambda environment.
    Uses multiple environment variables for robust detection.
    """
    lambda_indicators = [
        os.environ.get("AWS_LAMBDA_FUNCTION_NAME"),      # Always present in Lambda
        os.environ.get("AWS_LAMBDA_FUNCTION_VERSION"),   # Always present in Lambda  
        os.environ.get("LAMBDA_RUNTIME_DIR"),            # Present in newer Lambda runtimes
        os.environ.get("AWS_EXECUTION_ENV", "").startswith("AWS_Lambda_"),  # Original check
    ]
    
    # If any of these indicators are present, we're in Lambda
    return any(lambda_indicators)
```

### Environment Variables Checked
1. **`AWS_LAMBDA_FUNCTION_NAME`** - Always present in Lambda, contains the function name
2. **`AWS_LAMBDA_FUNCTION_VERSION`** - Always present in Lambda, contains the version
3. **`LAMBDA_RUNTIME_DIR`** - Present in newer Lambda runtimes, points to runtime directory
4. **`AWS_EXECUTION_ENV`** - Original check, kept for backward compatibility

## Implementation

### Files Modified
1. **`app2/utils/__init__.py`** - Created shared utility function
2. **`app2/config.py`** - Updated to use shared function
3. **`app2/slack/handler.py`** - Updated to use shared function  
4. **`app2/main.py`** - Updated to use shared function

### Benefits
- **Robust Detection**: Uses multiple indicators instead of single variable
- **Consistent Behavior**: Same detection logic across all modules
- **Debugging Support**: Logs all environment variables for troubleshooting
- **Backward Compatible**: Includes original check as fallback
- **DRY Principle**: Single shared function eliminates code duplication

## Debugging
The function logs all relevant environment variables when first called:

```
Lambda environment detection:
  AWS_LAMBDA_FUNCTION_NAME: my-function-name
  AWS_LAMBDA_FUNCTION_VERSION: $LATEST
  AWS_EXECUTION_ENV: AWS_Lambda_python3.9
  LAMBDA_RUNTIME_DIR: /var/runtime
  Detected as Lambda: True
```

## Testing
In local development, the detection correctly identifies non-Lambda environment:

```
Lambda environment detection:
  AWS_LAMBDA_FUNCTION_NAME: Not set
  AWS_LAMBDA_FUNCTION_VERSION: Not set
  AWS_EXECUTION_ENV: Dev
  LAMBDA_RUNTIME_DIR: Not set
  Detected as Lambda: False
```

## Impact on `/schedule_msg` Command
With this fix, the `/schedule_msg` command will now:
- Correctly detect Lambda environment when deployed
- Use the appropriate AWS Lambda Slack Request Handler
- Access AWS Secrets Manager for configuration
- Process scheduled messages with proper Lambda context

## Deployment
No additional configuration required. The fix is automatic and will take effect immediately upon deployment.

## Monitoring
Monitor Lambda logs for the "Lambda environment detection" messages to verify correct detection in production. 