services:
  chatbot:
    env_file: ".env"
    ports:
      - "5000:5000"
    # After running `docker buildx build --label lx-test-2408-01 --platform=linux/amd64 --tag disty/fastapi-salescompanion .`
    image: 989284569019.dkr.ecr.us-east-1.amazonaws.com/slackgenie-ecr:latest
    environment:
      APPS_USER: "admin" #${CHATBOT_BASIC_AUTH_USER}
      APPS_PASSWORD: "111" #${CHATBOT_BASIC_AUTH_PASSWORD}
  postgres:
    env_file: ".env"
    image: "postgres:13"
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: hi #${POSTGRES_USER}
      POSTGRES_PASSWORD: itsme #${POSTGRES_PASSWORD}
      POSTGRES_DB: demo_db #${POSTGRES_DB}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      
volumes:
  postgres_data: