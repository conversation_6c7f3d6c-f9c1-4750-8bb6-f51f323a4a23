#!/usr/bin/env python3
"""
Debug script to compare schedule creation vs listing with the same user ID.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app2.db.session import SessionLocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.slack.slack_app import SlackBotApp
import logging

# Set up logging to see what's happening
logging.basicConfig(level=logging.INFO)

def test_schedule_creation_and_listing():
    """Test both schedule creation and listing with the same user ID."""
    
    # Use the same user ID that's creating schedules
    test_user_id = "U01175Z2B8D"
    test_channel_id = "D08PFSNFRPD"  # From the database
    
    print(f"=== Testing with User ID: {test_user_id} ===")
    print()
    
    # First, check current state in database
    print("1. Current database state:")
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        user_schedules = repo.get_by_user(test_user_id)
        active_schedules = [s for s in user_schedules if s.is_active]
        
        print(f"   Total schedules for user: {len(user_schedules)}")
        print(f"   Active schedules for user: {len(active_schedules)}")
        
        for i, schedule in enumerate(active_schedules, 1):
            print(f"   {i}. ID: {schedule.id}")
            print(f"      Message: {schedule.message[:30]}...")
            print(f"      Active: {schedule.is_active}")
            print(f"      Created: {schedule.created_at}")
    print()
    
    # Test the list_schedules handler directly
    print("2. Testing list_schedules handler:")
    
    slack_app = SlackBotApp()
    
    # Mock the exact body that would come from Slack
    mock_body = {
        "user_id": test_user_id,
        "channel_id": test_channel_id,
        "text": "",
        "command": "/list_schedules"
    }
    
    class DebugClient:
        def chat_postEphemeral(self, **kwargs):
            print(f"   📤 Would send ephemeral message:")
            print(f"      Channel: {kwargs.get('channel')}")
            print(f"      User: {kwargs.get('user')}")
            print(f"      Text: {kwargs.get('text', '')[:100]}...")
            if 'blocks' in kwargs:
                blocks = kwargs.get('blocks', [])
                print(f"      Blocks: {len(blocks)} blocks")
                
                # Show block types for debugging
                for i, block in enumerate(blocks[:5]):  # Show first 5 blocks
                    block_type = block.get('type', 'unknown')
                    if block_type == 'section':
                        text = block.get('text', {}).get('text', '')[:50]
                        print(f"        Block {i}: {block_type} - {text}...")
                    else:
                        print(f"        Block {i}: {block_type}")
            print()
    
    debug_client = DebugClient()
    
    try:
        print("   Calling handle_list_schedules...")
        slack_app.handle_list_schedules(mock_body, debug_client)
        print("   ✅ Handler completed successfully")
    except Exception as e:
        print(f"   ❌ Handler failed: {e}")
        import traceback
        traceback.print_exc()
    
    print()
    
    # Test the repository method directly
    print("3. Testing repository method directly:")
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        
        print(f"   Calling repo.get_by_user('{test_user_id}')...")
        user_schedules = repo.get_by_user(test_user_id)
        print(f"   Result: {len(user_schedules)} schedules")
        
        print(f"   Filtering for active schedules...")
        active_schedules = [s for s in user_schedules if s.is_active]
        print(f"   Result: {len(active_schedules)} active schedules")
        
        if active_schedules:
            print("   Active schedules details:")
            for i, schedule in enumerate(active_schedules, 1):
                print(f"     {i}. ID: {schedule.id}")
                print(f"        User ID: {schedule.user_id}")
                print(f"        Message: {schedule.message}")
                print(f"        Is Active: {schedule.is_active}")
                print(f"        Channel: {schedule.channel_id}")
        else:
            print("   ⚠️  No active schedules found!")
    
    print()
    print("=== Debug Summary ===")
    print("If the repository finds schedules but the handler doesn't show them,")
    print("there might be an issue in the handler logic or Slack response.")

if __name__ == "__main__":
    test_schedule_creation_and_listing() 