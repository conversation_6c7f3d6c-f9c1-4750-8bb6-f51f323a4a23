import asyncio
import json
from datetime import datetime
import pytz
from croniter import croniter
from sqlalchemy.orm import Session
from slack_sdk import WebClient
import ssl
import certifi

from app2.db.session import SessionLocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.slack.slack_app import SlackBotApp
from app2.config import Settings

# Create SSL context for Slack client
ssl_context = ssl.create_default_context(cafile=certifi.where())
ssl_context.check_hostname = True
ssl_context.verify_mode = ssl.CERT_REQUIRED

# Initialize settings
settings = Settings()

async def simulate_eventbridge_trigger(schedule_id: str, channel_id: str, user_id: str, message: str):
    """Simulate an EventBridge trigger by calling the bot directly."""
    slack_app = SlackBotApp()
    
    # Initialize WebClient with SSL context
    client = WebClient(
        token=settings.slack_bot_token,
        ssl=ssl_context
    )
    
    # Create the same event structure that EventBridge would send
    event = {
        "type": "message",
        "channel": channel_id,
        "user": user_id,
        "text": message,
        "ts": str(datetime.now().timestamp()),
        "is_scheduled": True
    }
    
    # Process the message with the client
    slack_app.process_message(client, event)

async def test_schedule():
    """Test function to simulate schedule triggers."""
    db = SessionLocal()
    repo = RecurringScheduleRepository(db)
    
    try:
        # Get all active schedules
        schedules = repo.get_active()
        
        if not schedules:
            print("No active schedules found. Create a schedule using the Slack command first.")
            return

        print(f"Found {len(schedules)} active schedules")
        
        # Get current time in UTC
        now = datetime.now(pytz.UTC)
        
        for schedule in schedules:
            # Parse the cron expression
            cron = croniter(schedule.cron_expression, now)
            
            # Get next run time
            next_run = cron.get_next(datetime)
            print(f"\nSchedule ID: {schedule.id}")
            print(f"Message: {schedule.message}")
            print(f"Cron: {schedule.cron_expression}")
            print(f"Next run would be at: {next_run}")
            
            # Simulate the trigger
            print("\nSimulating EventBridge trigger...")
            await simulate_eventbridge_trigger(
                str(schedule.id),
                schedule.channel_id,
                schedule.user_id,
                schedule.message
            )
            print("Trigger simulation complete!")

    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_schedule()) 