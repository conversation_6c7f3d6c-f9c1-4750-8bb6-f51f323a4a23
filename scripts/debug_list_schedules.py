#!/usr/bin/env python3
"""
Debug script to test list_schedules functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app2.db.session import SessionLocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.slack.slack_app import SlackBotApp

def test_list_schedules_with_real_user():
    """Test list_schedules with the actual user ID that has schedules."""
    print("=== Testing list_schedules with real user ===")
    
    # First, check what's in the database
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        all_schedules = repo.get_active()
        print(f"Total active schedules in DB: {len(all_schedules)}")
        
        if all_schedules:
            real_user_id = all_schedules[0].user_id
            print(f"Using real user ID: {real_user_id}")
            
            # Get schedules for this user
            user_schedules = repo.get_by_user(real_user_id)
            active_schedules = [s for s in user_schedules if s.is_active]
            print(f"Active schedules for user {real_user_id}: {len(active_schedules)}")
            
            for i, schedule in enumerate(active_schedules, 1):
                print(f"  {i}. ID: {schedule.id}")
                print(f"     Message: {schedule.message[:50]}...")
                print(f"     Cron: {schedule.cron_expression}")
                print(f"     Active: {schedule.is_active}")
                print(f"     Created: {schedule.created_at}")
                print()
        else:
            print("No schedules found in database")
            return
    
    # Now test the SlackBotApp handler
    print("=== Testing SlackBotApp handler ===")
    
    slack_app = SlackBotApp()
    
    mock_body = {
        "user_id": real_user_id,
        "channel_id": "C12345TEST",
    }
    
    class MockClient:
        def chat_postEphemeral(self, **kwargs):
            print(f"Would send ephemeral message:")
            print(f"  Channel: {kwargs.get('channel')}")
            print(f"  User: {kwargs.get('user')}")
            print(f"  Text: {kwargs.get('text')}")
            if 'blocks' in kwargs:
                print(f"  Blocks: {len(kwargs['blocks'])} blocks")
                # Print first few blocks for debugging
                for i, block in enumerate(kwargs['blocks'][:3]):
                    print(f"    Block {i}: {block.get('type', 'unknown')}")
    
    mock_client = MockClient()
    
    try:
        slack_app.handle_list_schedules(mock_body, mock_client)
        print("✅ Handler executed successfully")
    except Exception as e:
        print(f"❌ Handler failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_list_schedules_with_real_user() 