#!/usr/bin/env python3
"""
Test script for one-time schedule functionality using EventBridge.
This script tests the new EventBridge-based one-time scheduling.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime, timezone, timedelta
from app2.db.session import SessionLocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.schemas.recurring_schedule import RecurringScheduleCreate
from app2.services.eventbridge import EventBridgeService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_one_time_schedule():
    """Test creating a one-time schedule using EventBridge."""
    logger.info("=== Testing One-Time Schedule with EventBridge ===")
    
    # Create a test schedule for 2 minutes from now
    future_time = datetime.now(timezone.utc) + timedelta(minutes=2)
    iso_time = future_time.strftime('%Y-%m-%dT%H:%M:%S')
    
    logger.info(f"Creating one-time schedule for: {iso_time}")
    
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        eventbridge_service = EventBridgeService()
        
        # Create the schedule in the database
        schedule_create = RecurringScheduleCreate(
            user_id="U12345TEST",
            channel_id="C12345TEST", 
            message="Test one-time message: What are the store quantity sold and returned, for each store in the state of Tennessee, also include store name",
            cron_expression=iso_time,  # Store ISO timestamp
            timezone="UTC",
            schedule_type="one_time",  # Mark as one-time schedule
            is_active=True,
        )
        
        db_schedule = repo.create(schedule_create)
        logger.info(f"Created database schedule with ID: {db_schedule.id}")
        
        # Test the EventBridge rule creation
        rule_arn = eventbridge_service.create_rule(db_schedule)
        
        if rule_arn:
            logger.info(f"✅ Successfully created EventBridge rule: {rule_arn}")
            
            # Update the schedule with the rule ARN
            repo.update_rule_arn(db_schedule.id, rule_arn)
            logger.info("✅ Updated schedule with rule ARN")
            
            # Test the _is_one_time_schedule method
            is_one_time = eventbridge_service._is_one_time_schedule(iso_time)
            logger.info(f"✅ _is_one_time_schedule correctly identified: {is_one_time}")
            
            logger.info("=== Test Summary ===")
            logger.info(f"Schedule ID: {db_schedule.id}")
            logger.info(f"Schedule Type: {db_schedule.schedule_type}")
            logger.info(f"Cron Expression (ISO): {db_schedule.cron_expression}")
            logger.info(f"EventBridge Rule ARN: {rule_arn}")
            logger.info(f"Scheduled Time: {iso_time}")
            
            return db_schedule.id
        else:
            logger.error("❌ Failed to create EventBridge rule")
            return None

def test_recurring_vs_one_time():
    """Test that the system can distinguish between recurring and one-time schedules."""
    logger.info("\n=== Testing Schedule Type Detection ===")
    
    eventbridge_service = EventBridgeService()
    
    # Test one-time schedule detection
    iso_time = "2024-12-27T15:30:00"
    is_one_time = eventbridge_service._is_one_time_schedule(iso_time)
    logger.info(f"ISO timestamp '{iso_time}' detected as one-time: {is_one_time}")
    
    # Test recurring schedule detection
    cron_expr = "30 15 * * MON *"
    is_recurring = not eventbridge_service._is_one_time_schedule(cron_expr)
    logger.info(f"Cron expression '{cron_expr}' detected as recurring: {is_recurring}")
    
    return is_one_time and is_recurring

def cleanup_test_schedules():
    """Clean up test schedules."""
    logger.info("\n=== Cleaning up test schedules ===")
    
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        eventbridge_service = EventBridgeService()
        
        # Get all schedules for test user
        test_schedules = repo.get_by_user("U12345TEST")
        
        for schedule in test_schedules:
            # Delete EventBridge rule if it exists
            if schedule.eventbridge_rule_arn:
                try:
                    eventbridge_service.delete_rule(schedule.id)
                    logger.info(f"Deleted EventBridge rule for schedule: {schedule.id}")
                except Exception as e:
                    logger.warning(f"Could not delete EventBridge rule: {e}")
            
            # Delete from database
            repo.delete(schedule.id)
            logger.info(f"Deleted test schedule: {schedule.id}")

if __name__ == "__main__":
    try:
        # Test schedule type detection
        detection_test = test_recurring_vs_one_time()
        
        if detection_test:
            logger.info("✅ Schedule type detection working correctly")
        else:
            logger.error("❌ Schedule type detection failed")
        
        # Test one-time schedule creation
        schedule_id = test_one_time_schedule()
        
        if schedule_id:
            logger.info("✅ One-time schedule test completed successfully")
            logger.info(f"Note: The schedule will trigger in 2 minutes. Check your Slack channel!")
        else:
            logger.error("❌ One-time schedule test failed")
        
        # Ask user if they want to clean up
        response = input("\nDo you want to clean up test schedules? (y/n): ")
        if response.lower() in ['y', 'yes']:
            cleanup_test_schedules()
            logger.info("✅ Cleanup completed")
        else:
            logger.info("Test schedules left in place for manual verification")
            
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc() 