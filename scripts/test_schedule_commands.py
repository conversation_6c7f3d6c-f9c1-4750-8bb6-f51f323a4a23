#!/usr/bin/env python3
"""
Test script for the new schedule management commands.
This script tests the /list_schedules and /delete_schedule commands.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app2.db.session import Session<PERSON>ocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.schemas.recurring_schedule import RecurringScheduleCreate
from app2.slack.slack_app import SlackBotApp
from slack_sdk import WebClient
import ssl
import certifi
from app2.config import Settings

# Create SSL context for Slack client
ssl_context = ssl.create_default_context(cafile=certifi.where())
ssl_context.check_hostname = True
ssl_context.verify_mode = ssl.CERT_REQUIRED

# Initialize settings
settings = Settings()

def create_test_schedule():
    """Create a test schedule for testing purposes."""
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        
        # Create a test schedule
        schedule_create = RecurringScheduleCreate(
            user_id="U12345TEST",
            channel_id="C12345TEST",
            message="Test daily report",
            cron_expression="0 9 * * MON-FRI *",
            timezone="UTC",
            is_active=True,
        )
        
        schedule = repo.create(schedule_create)
        print(f"Created test schedule with ID: {schedule.id}")
        return schedule

def test_list_schedules():
    """Test the list schedules functionality."""
    print("\n=== Testing List Schedules ===")
    
    # Create a test schedule first
    test_schedule = create_test_schedule()
    
    slack_app = SlackBotApp()
    
    # Mock body for list_schedules command
    mock_body = {
        "user_id": "U12345TEST",
        "channel_id": "C12345TEST",
        "text": "",
    }
    
    # Mock client (we'll just print instead of actually sending to Slack)
    class MockClient:
        def chat_postEphemeral(self, **kwargs):
            print(f"Would send ephemeral message:")
            print(f"  Channel: {kwargs.get('channel')}")
            print(f"  User: {kwargs.get('user')}")
            print(f"  Text: {kwargs.get('text')}")
            if 'blocks' in kwargs:
                print(f"  Blocks: {len(kwargs['blocks'])} blocks")
    
    mock_client = MockClient()
    
    try:
        slack_app.handle_list_schedules(mock_body, mock_client)
        print("✅ List schedules test completed successfully")
    except Exception as e:
        print(f"❌ List schedules test failed: {e}")
    
    return test_schedule

def test_delete_schedule(schedule_id):
    """Test the delete schedule functionality."""
    print(f"\n=== Testing Delete Schedule (ID: {schedule_id}) ===")
    
    slack_app = SlackBotApp()
    
    # Mock body for delete_schedule command
    mock_body = {
        "user_id": "U12345TEST",
        "channel_id": "C12345TEST",
        "text": str(schedule_id),
    }
    
    # Mock client
    class MockClient:
        def chat_postEphemeral(self, **kwargs):
            print(f"Would send ephemeral message:")
            print(f"  Channel: {kwargs.get('channel')}")
            print(f"  User: {kwargs.get('user')}")
            print(f"  Text: {kwargs.get('text')}")
            if 'blocks' in kwargs:
                print(f"  Blocks: {len(kwargs['blocks'])} blocks")
    
    mock_client = MockClient()
    
    try:
        slack_app.handle_delete_schedule(mock_body, mock_client)
        print("✅ Delete schedule test completed successfully")
    except Exception as e:
        print(f"❌ Delete schedule test failed: {e}")

def cleanup_test_schedules():
    """Clean up any test schedules."""
    print("\n=== Cleaning up test schedules ===")
    
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        
        # Get all schedules for test user
        test_schedules = repo.get_by_user("U12345TEST")
        
        for schedule in test_schedules:
            repo.delete(schedule.id)
            print(f"Deleted test schedule: {schedule.id}")

def main():
    """Run the test suite."""
    print("🧪 Testing Schedule Management Commands")
    print("=" * 50)
    
    try:
        # Test list schedules (this will create a test schedule)
        test_schedule = test_list_schedules()
        
        # Test delete schedule
        test_delete_schedule(test_schedule.id)
        
        # Test list schedules again (should show no schedules)
        print("\n=== Testing List Schedules (After Deletion) ===")
        slack_app = SlackBotApp()
        mock_body = {
            "user_id": "U12345TEST",
            "channel_id": "C12345TEST",
            "text": "",
        }
        
        class MockClient:
            def chat_postEphemeral(self, **kwargs):
                print(f"Would send ephemeral message:")
                print(f"  Text: {kwargs.get('text')}")
        
        slack_app.handle_list_schedules(mock_body, MockClient())
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up any remaining test schedules
        cleanup_test_schedules()

if __name__ == "__main__":
    main() 