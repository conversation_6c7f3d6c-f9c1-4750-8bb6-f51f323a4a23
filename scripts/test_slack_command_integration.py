#!/usr/bin/env python3
"""
Test script for Slack command integration with EventBridge one-time schedules.
This script tests the command parsing and database operations without requiring AWS permissions.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from datetime import datetime, timezone, timedelta
from app2.db.session import SessionLocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.schemas.recurring_schedule import RecurringScheduleCreate
from app2.services.eventbridge import EventBridgeService
from app2.slack.slack_app import SlackBotApp
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_datetime_parsing():
    """Test the datetime parsing functionality."""
    logger.info("=== Testing Datetime Parsing ===")
    
    slack_app = SlackBotApp()
    
    # Test valid datetime formats
    test_cases = [
        "2025-05-27 18:14",
        "2024-12-31 23:59",
        "2025-01-01 00:00",
    ]
    
    for test_case in test_cases:
        try:
            timestamp = slack_app._convert_to_timestamp(test_case)
            dt = datetime.fromtimestamp(timestamp, tz=timezone.utc)
            iso_time = dt.strftime('%Y-%m-%dT%H:%M:%S')
            logger.info(f"✅ '{test_case}' → {timestamp} → {iso_time}")
        except Exception as e:
            logger.error(f"❌ '{test_case}' failed: {e}")
    
    return True

def test_schedule_type_detection():
    """Test schedule type detection logic."""
    logger.info("\n=== Testing Schedule Type Detection ===")
    
    eventbridge_service = EventBridgeService()
    
    test_cases = [
        ("2025-05-27T18:14:00", True, "ISO timestamp"),
        ("0 9 * * ? *", False, "Cron expression"),
        ("30 15 * * MON *", False, "Cron with day"),
        ("2024-12-31T23:59:59", True, "ISO with seconds"),
    ]
    
    all_passed = True
    for expression, expected_one_time, description in test_cases:
        result = eventbridge_service._is_one_time_schedule(expression)
        if result == expected_one_time:
            logger.info(f"✅ {description}: '{expression}' → {result}")
        else:
            logger.error(f"❌ {description}: '{expression}' → {result} (expected {expected_one_time})")
            all_passed = False
    
    return all_passed

def test_database_operations():
    """Test database operations for both schedule types."""
    logger.info("\n=== Testing Database Operations ===")
    
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        
        # Test creating a one-time schedule
        future_time = datetime.now(timezone.utc) + timedelta(hours=1)
        iso_time = future_time.strftime('%Y-%m-%dT%H:%M:%S')
        
        one_time_schedule = RecurringScheduleCreate(
            user_id="U_TEST_USER",
            channel_id="C_TEST_CHANNEL",
            message="Test one-time message: What are the store quantity sold and returned?",
            cron_expression=iso_time,
            timezone="UTC",
            schedule_type="one_time",
            is_active=True,
        )
        
        try:
            db_schedule = repo.create(one_time_schedule)
            logger.info(f"✅ Created one-time schedule: {db_schedule.id}")
            logger.info(f"   Schedule type: {db_schedule.schedule_type}")
            logger.info(f"   Cron expression: {db_schedule.cron_expression}")
            
            # Test creating a recurring schedule
            recurring_schedule = RecurringScheduleCreate(
                user_id="U_TEST_USER",
                channel_id="C_TEST_CHANNEL", 
                message="Test recurring message: Daily sales report",
                cron_expression="0 9 * * ? *",
                timezone="UTC",
                schedule_type="recurring",
                is_active=True,
            )
            
            db_recurring = repo.create(recurring_schedule)
            logger.info(f"✅ Created recurring schedule: {db_recurring.id}")
            logger.info(f"   Schedule type: {db_recurring.schedule_type}")
            logger.info(f"   Cron expression: {db_recurring.cron_expression}")
            
            # Test listing schedules
            user_schedules = repo.get_by_user("U_TEST_USER")
            logger.info(f"✅ Found {len(user_schedules)} schedules for test user")
            
            # Test schedule display formatting
            slack_app = SlackBotApp()
            for schedule in user_schedules:
                display = slack_app._format_schedule_display(schedule)
                logger.info(f"   Display format: {display.replace(chr(10), ' | ')}")
            
            # Clean up
            for schedule in user_schedules:
                repo.delete(schedule.id)
            logger.info("✅ Cleaned up test schedules")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Database operation failed: {e}")
            return False

def test_command_parsing():
    """Test command parsing logic."""
    logger.info("\n=== Testing Command Parsing ===")
    
    test_commands = [
        {
            "text": "What are the store quantity sold and returned, for each store in the state of Tennessee, also include store name | 2025-05-27 18:14",
            "expected_type": "one_time",
            "description": "One-time schedule command"
        },
        {
            "text": "Daily sales report | every day at 9am",
            "expected_type": "recurring", 
            "description": "Recurring schedule command"
        },
        {
            "text": "Weekly review | every friday at 4pm",
            "expected_type": "recurring",
            "description": "Weekly recurring command"
        }
    ]
    
    eventbridge_service = EventBridgeService()
    all_passed = True
    
    for test_case in test_commands:
        try:
            parts = test_case["text"].split("|", 1)
            if len(parts) != 2:
                logger.error(f"❌ Invalid command format: {test_case['text']}")
                all_passed = False
                continue
                
            message = parts[0].strip()
            schedule_str = parts[1].strip()
            
            if "every" in schedule_str.lower():
                # Recurring schedule
                cron_expr = eventbridge_service.parse_cron(schedule_str)
                if cron_expr and test_case["expected_type"] == "recurring":
                    logger.info(f"✅ {test_case['description']}: '{schedule_str}' → {cron_expr}")
                else:
                    logger.error(f"❌ {test_case['description']}: Failed to parse recurring schedule")
                    all_passed = False
            else:
                # One-time schedule
                if test_case["expected_type"] == "one_time":
                    logger.info(f"✅ {test_case['description']}: '{schedule_str}' → one-time")
                else:
                    logger.error(f"❌ {test_case['description']}: Unexpected one-time detection")
                    all_passed = False
                    
        except Exception as e:
            logger.error(f"❌ {test_case['description']}: {e}")
            all_passed = False
    
    return all_passed

def main():
    """Run all tests."""
    logger.info("🚀 Starting Slack Command Integration Tests")
    
    tests = [
        ("Datetime Parsing", test_datetime_parsing),
        ("Schedule Type Detection", test_schedule_type_detection),
        ("Database Operations", test_database_operations),
        ("Command Parsing", test_command_parsing),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*50)
    logger.info("TEST SUMMARY")
    logger.info("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} {test_name}")
        if result:
            passed += 1
    
    logger.info(f"\nResults: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        logger.info("🎉 All tests passed! The EventBridge one-time schedule integration is working correctly.")
        logger.info("\nNext steps:")
        logger.info("1. Deploy to Lambda with proper IAM permissions")
        logger.info("2. Test with actual Slack commands")
        logger.info("3. Verify schedules trigger at specified times")
    else:
        logger.error("❌ Some tests failed. Please review the errors above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 