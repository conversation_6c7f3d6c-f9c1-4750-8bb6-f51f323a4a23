#!/usr/bin/env python3
"""
Test script for the delete schedule button endpoint.
This script tests that the FastAPI endpoint can handle delete_schedule_button actions.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app2.db.session import SessionLocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository
from app2.schemas.recurring_schedule import RecurringScheduleCreate
from app2.schemas.slack import SlackInteractionPayload, SlackAction, SlackUser, SlackChannel
import json

def create_test_schedule():
    """Create a test schedule for testing purposes."""
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        
        # Create a test schedule
        schedule_create = RecurringScheduleCreate(
            user_id="U12345TEST",
            channel_id="C12345TEST",
            message="Test daily report for button deletion",
            cron_expression="0 9 * * MON-FRI *",
            timezone="UTC",
            is_active=True,
        )
        
        schedule = repo.create(schedule_create)
        print(f"Created test schedule with ID: {schedule.id}")
        return schedule

def test_delete_button_payload():
    """Test creating a valid delete button payload."""
    print("\n=== Testing Delete Button Payload Creation ===")
    
    # Create a test schedule
    test_schedule = create_test_schedule()
    
    # Create a mock payload that would come from Slack
    mock_payload = {
        "type": "block_actions",
        "actions": [
            {
                "action_id": "delete_schedule_button",
                "type": "button",
                "value": str(test_schedule.id)
            }
        ],
        "user": {
            "id": "U12345TEST"
        },
        "channel": {
            "id": "C12345TEST"
        },
        "message": {
            "ts": "1234567890.123456",
            "text": "Test message",
            "blocks": []
        }
    }
    
    try:
        # Test that we can parse the payload
        interaction_data = SlackInteractionPayload.model_validate(mock_payload)
        print(f"✅ Successfully parsed interaction payload")
        print(f"   Action ID: {interaction_data.actions[0].action_id}")
        print(f"   Schedule ID: {interaction_data.actions[0].value}")
        print(f"   User ID: {interaction_data.user.id}")
        
        # Test the delete operation directly
        with SessionLocal() as db:
            repo = RecurringScheduleRepository(db)
            success, message = repo.delete_schedule(test_schedule.id, "U12345TEST")
            
            if success:
                print(f"✅ Delete operation successful: {message}")
            else:
                print(f"❌ Delete operation failed: {message}")
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    return test_schedule

def cleanup_test_schedules():
    """Clean up any remaining test schedules."""
    print("\n=== Cleaning up test schedules ===")
    
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        
        # Get all schedules for test user
        test_schedules = repo.get_by_user("U12345TEST")
        
        for schedule in test_schedules:
            repo.delete(schedule.id)
            print(f"Deleted test schedule: {schedule.id}")

def main():
    """Run the test suite."""
    print("🧪 Testing Delete Schedule Button Endpoint")
    print("=" * 50)
    
    try:
        # Test the payload creation and parsing
        test_delete_button_payload()
        
        print("\n✅ All endpoint tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up any remaining test schedules
        cleanup_test_schedules()

if __name__ == "__main__":
    main() 