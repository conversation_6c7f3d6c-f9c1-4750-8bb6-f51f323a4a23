#!/usr/bin/env python3
"""
Script to check user IDs and help debug the list_schedules issue.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app2.db.session import SessionLocal
from app2.db.repositories.recurring_schedule import RecurringScheduleRepository

def check_user_ids():
    """Check what user IDs have schedules in the database."""
    print("=== User IDs with schedules in database ===")
    
    with SessionLocal() as db:
        repo = RecurringScheduleRepository(db)
        all_schedules = repo.get_active()
        
        # Group schedules by user ID
        user_schedules = {}
        for schedule in all_schedules:
            user_id = schedule.user_id
            if user_id not in user_schedules:
                user_schedules[user_id] = []
            user_schedules[user_id].append(schedule)
        
        print(f"Total active schedules: {len(all_schedules)}")
        print(f"Number of users with schedules: {len(user_schedules)}")
        print()
        
        for user_id, schedules in user_schedules.items():
            print(f"User ID: {user_id}")
            print(f"  Number of active schedules: {len(schedules)}")
            for i, schedule in enumerate(schedules, 1):
                print(f"    {i}. {schedule.message[:40]}... (Created: {schedule.created_at})")
            print()
        
        # Show the most recent schedule details
        if all_schedules:
            latest_schedule = max(all_schedules, key=lambda s: s.created_at)
            print("=== Most Recent Schedule ===")
            print(f"User ID: {latest_schedule.user_id}")
            print(f"Message: {latest_schedule.message}")
            print(f"Cron: {latest_schedule.cron_expression}")
            print(f"Created: {latest_schedule.created_at}")
            print(f"Channel: {latest_schedule.channel_id}")
            print()
            
            print("💡 When you test /list_schedules in Slack, make sure you're using the same user that created the schedules!")
            print(f"💡 The schedules were created by user: {latest_schedule.user_id}")

if __name__ == "__main__":
    check_user_ids() 