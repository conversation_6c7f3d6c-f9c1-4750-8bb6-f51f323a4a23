#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update AWS Secrets Manager with Lambda and EventBridge ARNs.

This script helps you add the required LAMBDA_ARN and EVENTBRIDGE_ROLE_ARN
to your AWS Secrets Manager secret for the SlackGenie application.

Usage:
    python scripts/update_aws_secrets.py --lambda-arn <arn> --eventbridge-role-arn <arn>

Example:
    python scripts/update_aws_secrets.py \
        --lambda-arn "arn:aws:lambda:us-east-1:989284569019:function:slackgenie-lambda" \
        --eventbridge-role-arn "arn:aws:iam::989284569019:role/EventBridgeInvokeLambdaRole"
"""

import argparse
import json
import boto3
import sys
from typing import Dict, Any


def get_current_secret(secret_arn: str) -> Dict[str, Any]:
    """Retrieve current secret values from AWS Secrets Manager."""
    client = boto3.client('secretsmanager', region_name='us-east-1')
    
    try:
        response = client.get_secret_value(SecretId=secret_arn)
        return json.loads(response['SecretString'])
    except Exception as e:
        print(f"Error retrieving secret: {e}")
        sys.exit(1)


def update_secret(secret_arn: str, secret_dict: Dict[str, Any]) -> None:
    """Update secret in AWS Secrets Manager."""
    client = boto3.client('secretsmanager', region_name='us-east-1')
    
    try:
        response = client.update_secret(
            SecretId=secret_arn,
            SecretString=json.dumps(secret_dict)
        )
        print(f"Successfully updated secret: {response['ARN']}")
    except Exception as e:
        print(f"Error updating secret: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Update AWS Secrets Manager with Lambda and EventBridge ARNs"
    )
    parser.add_argument(
        "--lambda-arn",
        required=True,
        help="ARN of the Lambda function (e.g., arn:aws:lambda:us-east-1:123456789012:function:my-function)"
    )
    parser.add_argument(
        "--eventbridge-role-arn", 
        required=True,
        help="ARN of the EventBridge IAM role (e.g., arn:aws:iam::123456789012:role/EventBridgeInvokeLambdaRole)"
    )
    parser.add_argument(
        "--secret-arn",
        default="arn:aws:secretsmanager:us-east-1:989284569019:secret:slackgenie/dev/app-secrets-SOd2JS",
        help="ARN of the AWS Secrets Manager secret to update"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be updated without making changes"
    )
    
    args = parser.parse_args()
    
    print(f"Retrieving current secret from: {args.secret_arn}")
    current_secret = get_current_secret(args.secret_arn)
    
    print(f"Current secret keys: {list(current_secret.keys())}")
    
    # Add the new values
    current_secret["LAMBDA_ARN"] = args.lambda_arn
    current_secret["EVENTBRIDGE_ROLE_ARN"] = args.eventbridge_role_arn
    
    print(f"\nUpdated secret will contain:")
    for key, value in current_secret.items():
        if key in ["LAMBDA_ARN", "EVENTBRIDGE_ROLE_ARN"]:
            print(f"  {key}: {value}")
        else:
            # Mask other values for security
            print(f"  {key}: {'*' * len(str(value))}")
    
    if args.dry_run:
        print("\n[DRY RUN] Would update secret with the above values")
        return
    
    print(f"\nUpdating secret...")
    update_secret(args.secret_arn, current_secret)
    print("✅ Secret updated successfully!")
    
    print("\n🚀 You can now redeploy your Lambda function to use the updated configuration.")


if __name__ == "__main__":
    main() 