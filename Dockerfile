# syntax=docker/dockerfile:1
# Keep this syntax directive! It's used to enable Docker BuildKit

FROM python:3.12.6-alpine AS base

RUN --mount=type=cache,target=/var/lib/apt/lists \
    --mount=type=cache,target=/var/cache,sharing=locked \
    apk update
    
RUN apk add --no-cache gcc musl-dev python3-dev g++

ENV USE_ROCM=0 \
    USE_XPU=0 \
    USE_CUDA=0 \
    PYTHONUNBUFFERED=1 \
    # prevents python creating .pyc files
    PYTHONDONTWRITEBYTECODE=1 \
    # Add /code to Python path
    PYTHONPATH=/code \
    \
    # pip
    PIP_NO_CACHE_DIR=off \
    PIP_DISABLE_PIP_VERSION_CHECK=on \
    PIP_DEFAULT_TIMEOUT=100 \
    \
    # poetry
    # https://python-poetry.org/docs/configuration/#using-environment-variables
    POETRY_VERSION=2.1.1 \
    # make poetry install to this location
    POETRY_HOME="/opt/poetry" \
    # make poetry create the virtual environment in the project's root
    # it gets named `.venv`
    POETRY_VIRTUALENVS_CREATE=1 \
    POETRY_VIRTUALENVS_IN_PROJECT=1 \
    # do not ask any interactive question
    POETRY_NO_INTERACTION=1 \
    POETRY_CACHE_DIR=/tmp/poetry_cache \
    \
    # paths
    # this is where our requirements + virtual environment will live
    PYSETUP_PATH="/opt/pysetup" \
    VENV_PATH="/poetry"

# prepend poetry and venv to path
ENV PATH="$POETRY_HOME/bin:$VENV_PATH/bin:$PATH"

FROM amazonlinux:2023

# Update all packages
RUN yum update -y

# Install specific package versions that address the vulnerabilities found by Vanta
RUN yum install -y \
    libxml2 >= 2.10.4-1.amzn2023.0.9 \
    libcap >= 2.48-2.amzn2023.0.4

# Update vulnerable packages
RUN yum update -y \
    libxml2 \
    sqlite-libs && \
    yum clean all

FROM base AS poetry

WORKDIR /tmp

# Install poetry
RUN --mount=type=cache,target=/root/.cache \
    python3 -m pip install poetry==$POETRY_VERSION

# Copy poetry configuration files
COPY poetry.lock pyproject.toml ./

# Install the export plugin and export dependencies
RUN poetry self add poetry-plugin-export && \
    poetry export --output=requirements.txt --format=requirements.txt --without-hashes

FROM base AS runtime

# Install dependencies from the exported requirements file
RUN --mount=type=cache,target=/root/.cache \
    --mount=type=bind,from=poetry,source=/tmp,target=/poetry \
    python3 -m pip install --disable-pip-version-check --no-deps --requirement=/poetry/requirements.txt

COPY --chown=app:app ./ /code/

WORKDIR /code

EXPOSE 80
EXPOSE 8080

# Create a startup script that runs migrations before starting the app
RUN printf '#!/bin/sh\necho "Running database migrations..."\npython -m alembic upgrade head\necho "Starting application..."\npython /code/app2/main.py\n' > /startup.sh && chmod +x /startup.sh

CMD ["/startup.sh"]