<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: SlackGenie AWS Architecture Pages: 1 -->
<svg width="1053pt" height="862pt"
 viewBox="0.00 0.00 1053.20 861.75" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(144 717.75)">
<title>SlackGenie AWS Architecture</title>
<polygon fill="white" stroke="none" points="-144,144 -144,-717.75 909.2,-717.75 909.2,144 -144,144"/>
<text text-anchor="middle" x="382.6" y="-5.5" font-family="Sans-Serif" font-size="15.00" fill="#2d3436">SlackGenie AWS Architecture</text>
<g id="clust1" class="cluster">
<title>cluster_API Layer</title>
<path fill="#e5f5fd" stroke="#aeb6be" d="M20,-392.75C20,-392.75 267.6,-392.75 267.6,-392.75 273.6,-392.75 279.6,-398.75 279.6,-404.75 279.6,-404.75 279.6,-553.75 279.6,-553.75 279.6,-559.75 273.6,-565.75 267.6,-565.75 267.6,-565.75 20,-565.75 20,-565.75 14,-565.75 8,-559.75 8,-553.75 8,-553.75 8,-404.75 8,-404.75 8,-398.75 14,-392.75 20,-392.75"/>
<text text-anchor="middle" x="42.25" y="-550.35" font-family="Sans-Serif" font-size="12.00" fill="#2d3436">API Layer</text>
</g>
<g id="clust2" class="cluster">
<title>cluster_Compute Layer</title>
<path fill="#e5f5fd" stroke="#aeb6be" d="M174.8,-211.75C174.8,-211.75 422.4,-211.75 422.4,-211.75 428.4,-211.75 434.4,-217.75 434.4,-223.75 434.4,-223.75 434.4,-372.75 434.4,-372.75 434.4,-378.75 428.4,-384.75 422.4,-384.75 422.4,-384.75 174.8,-384.75 174.8,-384.75 168.8,-384.75 162.8,-378.75 162.8,-372.75 162.8,-372.75 162.8,-223.75 162.8,-223.75 162.8,-217.75 168.8,-211.75 174.8,-211.75"/>
<text text-anchor="middle" x="211.67" y="-369.35" font-family="Sans-Serif" font-size="12.00" fill="#2d3436">Compute Layer</text>
</g>
<g id="clust3" class="cluster">
<title>cluster_Storage &amp; State</title>
<path fill="#e5f5fd" stroke="#aeb6be" d="M490.5,-392.75C490.5,-392.75 745.2,-392.75 745.2,-392.75 751.2,-392.75 757.2,-398.75 757.2,-404.75 757.2,-404.75 757.2,-553.75 757.2,-553.75 757.2,-559.75 751.2,-565.75 745.2,-565.75 745.2,-565.75 490.5,-565.75 490.5,-565.75 484.5,-565.75 478.5,-559.75 478.5,-553.75 478.5,-553.75 478.5,-404.75 478.5,-404.75 478.5,-398.75 484.5,-392.75 490.5,-392.75"/>
<text text-anchor="middle" x="528.5" y="-550.35" font-family="Sans-Serif" font-size="12.00" fill="#2d3436">Storage &amp; State</text>
</g>
<g id="clust4" class="cluster">
<title>cluster_Monitoring &amp; Security</title>
<path fill="#e5f5fd" stroke="#aeb6be" d="M484.4,-31.75C484.4,-31.75 590.4,-31.75 590.4,-31.75 596.4,-31.75 602.4,-37.75 602.4,-43.75 602.4,-43.75 602.4,-372.75 602.4,-372.75 602.4,-378.75 596.4,-384.75 590.4,-384.75 590.4,-384.75 484.4,-384.75 484.4,-384.75 478.4,-384.75 472.4,-378.75 472.4,-372.75 472.4,-372.75 472.4,-43.75 472.4,-43.75 472.4,-37.75 478.4,-31.75 484.4,-31.75"/>
<text text-anchor="middle" x="537.4" y="-369.35" font-family="Sans-Serif" font-size="12.00" fill="#2d3436">Monitoring &amp; Security</text>
</g>
<g id="clust5" class="cluster">
<title>cluster_CI/CD Pipeline</title>
<path fill="#e5f5fd" stroke="#aeb6be" d="M20,-211.75C20,-211.75 112.8,-211.75 112.8,-211.75 118.8,-211.75 124.8,-217.75 124.8,-223.75 124.8,-223.75 124.8,-372.75 124.8,-372.75 124.8,-378.75 118.8,-384.75 112.8,-384.75 112.8,-384.75 20,-384.75 20,-384.75 14,-384.75 8,-378.75 8,-372.75 8,-372.75 8,-223.75 8,-223.75 8,-217.75 14,-211.75 20,-211.75"/>
<text text-anchor="middle" x="56.12" y="-369.35" font-family="Sans-Serif" font-size="12.00" fill="#2d3436">CI/CD Pipeline</text>
</g>
<!-- afb7aef276e54e7d825e42fc99139bfb -->
<g id="node1" class="node">
<title>afb7aef276e54e7d825e42fc99139bfb</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/security/waf.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="16" y="-519.15"/>
<text text-anchor="middle" x="66.4" y="-401.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">WAF</text>
</g>
<!-- 721748a1f89d40919c9e92f5b73d3323 -->
<g id="node2" class="node">
<title>721748a1f89d40919c9e92f5b73d3323</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/network/api-gateway.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="170.8" y="-519.15"/>
<text text-anchor="middle" x="221.2" y="-401.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">API Gateway</text>
</g>
<!-- afb7aef276e54e7d825e42fc99139bfb&#45;&gt;721748a1f89d40919c9e92f5b73d3323 -->
<g id="edge1" class="edge">
<title>afb7aef276e54e7d825e42fc99139bfb&#45;&gt;721748a1f89d40919c9e92f5b73d3323</title>
<path fill="none" stroke="#7b8894" d="M116.66,-468.75C116.66,-468.75 159.41,-468.75 159.41,-468.75"/>
<polygon fill="#7b8894" stroke="#7b8894" points="159.41,-472.25 169.41,-468.75 159.41,-465.25 159.41,-472.25"/>
</g>
<!-- 2421edf2b7e940e6a52c846be9919729 -->
<g id="node4" class="node">
<title>2421edf2b7e940e6a52c846be9919729</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/compute/lambda.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="325.6" y="-338.15"/>
<text text-anchor="middle" x="376" y="-220.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">FastAPI Lambda</text>
</g>
<!-- 721748a1f89d40919c9e92f5b73d3323&#45;&gt;2421edf2b7e940e6a52c846be9919729 -->
<g id="edge5" class="edge">
<title>721748a1f89d40919c9e92f5b73d3323&#45;&gt;2421edf2b7e940e6a52c846be9919729</title>
<path fill="none" stroke="#7b8894" d="M271.41,-491.55C310.87,-491.55 359.2,-491.55 359.2,-491.55 359.2,-491.55 359.2,-367.41 359.2,-367.41"/>
<polygon fill="#7b8894" stroke="#7b8894" points="362.7,-367.41 359.2,-357.41 355.7,-367.41 362.7,-367.41"/>
</g>
<!-- 51890c80c38a45b3aeca60851224e9d3 -->
<g id="node3" class="node">
<title>51890c80c38a45b3aeca60851224e9d3</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/compute/ec2-container-registry.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="170.8" y="-338.15"/>
<text text-anchor="middle" x="221.2" y="-220.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">Container Registry</text>
</g>
<!-- 51890c80c38a45b3aeca60851224e9d3&#45;&gt;2421edf2b7e940e6a52c846be9919729 -->
<g id="edge2" class="edge">
<title>51890c80c38a45b3aeca60851224e9d3&#45;&gt;2421edf2b7e940e6a52c846be9919729</title>
<path fill="none" stroke="#7b8894" d="M271.46,-287.75C288.85,-287.75 308.34,-287.75 325.73,-287.75"/>
</g>
<!-- fc83a9f4cc1448f3aab65b2d3fb0a307 -->
<g id="node5" class="node">
<title>fc83a9f4cc1448f3aab65b2d3fb0a307</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/storage/simple-storage-service-s3.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="486.5" y="-519.15"/>
<text text-anchor="middle" x="536.9" y="-401.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">Terraform State</text>
</g>
<!-- 2421edf2b7e940e6a52c846be9919729&#45;&gt;fc83a9f4cc1448f3aab65b2d3fb0a307 -->
<g id="edge8" class="edge">
<title>2421edf2b7e940e6a52c846be9919729&#45;&gt;fc83a9f4cc1448f3aab65b2d3fb0a307</title>
<path fill="none" stroke="#7b8894" d="M393.2,-355.76C393.2,-398.64 393.2,-445.95 393.2,-445.95 393.2,-445.95 475.13,-445.95 475.13,-445.95"/>
<polygon fill="#7b8894" stroke="#7b8894" points="475.13,-449.45 485.13,-445.95 475.13,-442.45 475.13,-449.45"/>
</g>
<!-- d50e57619bed4f2da6a9d511528b7a66 -->
<g id="node7" class="node">
<title>d50e57619bed4f2da6a9d511528b7a66</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/management/cloudwatch.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="486.5" y="-158.15"/>
<text text-anchor="middle" x="536.9" y="-40.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">CloudWatch</text>
</g>
<!-- 2421edf2b7e940e6a52c846be9919729&#45;&gt;d50e57619bed4f2da6a9d511528b7a66 -->
<g id="edge6" class="edge">
<title>2421edf2b7e940e6a52c846be9919729&#45;&gt;d50e57619bed4f2da6a9d511528b7a66</title>
<path fill="none" stroke="#7b8894" d="M376.2,-219.44C376.2,-168.52 376.2,-107.75 376.2,-107.75 376.2,-107.75 475.42,-107.75 475.42,-107.75"/>
<polygon fill="#7b8894" stroke="#7b8894" points="475.42,-111.25 485.42,-107.75 475.42,-104.25 475.42,-111.25"/>
</g>
<!-- 329b8ba6adcb4265b36cc6ba64d73888 -->
<g id="node6" class="node">
<title>329b8ba6adcb4265b36cc6ba64d73888</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/database/dynamodb.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="648.4" y="-519.15"/>
<text text-anchor="middle" x="698.8" y="-401.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">State Locks</text>
</g>
<!-- fc83a9f4cc1448f3aab65b2d3fb0a307&#45;&gt;329b8ba6adcb4265b36cc6ba64d73888 -->
<g id="edge3" class="edge">
<title>fc83a9f4cc1448f3aab65b2d3fb0a307&#45;&gt;329b8ba6adcb4265b36cc6ba64d73888</title>
<path fill="none" stroke="#7b8894" d="M587.24,-468.75C606.73,-468.75 629,-468.75 648.49,-468.75"/>
</g>
<!-- c8f960086b864bfaa2968535a26f6a17 -->
<g id="node8" class="node">
<title>c8f960086b864bfaa2968535a26f6a17</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/security/identity-and-access-management-iam.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="486.5" y="-338.15"/>
<text text-anchor="middle" x="536.9" y="-220.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">IAM Roles</text>
</g>
<!-- c8f960086b864bfaa2968535a26f6a17&#45;&gt;2421edf2b7e940e6a52c846be9919729 -->
<g id="edge7" class="edge">
<title>c8f960086b864bfaa2968535a26f6a17&#45;&gt;2421edf2b7e940e6a52c846be9919729</title>
<path fill="none" stroke="#7b8894" d="M486.87,-287.75C467.59,-287.75 445.58,-287.75 426.27,-287.75"/>
</g>
<!-- 1a03ebc4468a4f76bf31cb3dc0666426 -->
<g id="node9" class="node">
<title>1a03ebc4468a4f76bf31cb3dc0666426</title>
<image xlink:href="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/devtools/codebuild.png" width="100.8px" height="100.8px" preserveAspectRatio="xMinYMin meet" x="16" y="-338.15"/>
<text text-anchor="middle" x="66.4" y="-220.5" font-family="Sans-Serif" font-size="13.00" fill="#2d3436">CodeBuild</text>
</g>
<!-- 1a03ebc4468a4f76bf31cb3dc0666426&#45;&gt;51890c80c38a45b3aeca60851224e9d3 -->
<g id="edge4" class="edge">
<title>1a03ebc4468a4f76bf31cb3dc0666426&#45;&gt;51890c80c38a45b3aeca60851224e9d3</title>
<path fill="none" stroke="#7b8894" d="M116.66,-287.75C116.66,-287.75 159.41,-287.75 159.41,-287.75"/>
<polygon fill="#7b8894" stroke="#7b8894" points="159.41,-291.25 169.41,-287.75 159.41,-284.25 159.41,-291.25"/>
</g>
</g>
</svg>
