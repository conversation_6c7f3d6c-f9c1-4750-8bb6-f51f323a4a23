digraph "SlackGenie AWS Architecture" {
	graph [bb="0,0,765.2,573.75",
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=15,
		label="SlackGenie AWS Architecture",
		lheight=0.22,
		lp="382.6,11.875",
		lwidth=2.73,
		nodesep=0.60,
		pad=2.0,
		rankdir=LR,
		ranksep=0.75,
		splines=ortho
	];
	node [fixedsize=true,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		height=1.4,
		imagescale=true,
		label="\N",
		labelloc=b,
		shape=box,
		style=rounded,
		width=1.4
	];
	edge [color="#7B8894"];
	subgraph "cluster_API Layer" {
		graph [bb="8,392.75,279.6,565.75",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label="API Layer",
			labeljust=l,
			lheight=0.18,
			lp="42.25,555.38",
			lwidth=0.73,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		********************************	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/security/waf.png",
			label=WAF,
			pos="66.4,468.75",
			shape=none];
		"721748a1f89d40919c9e92f5b73d3323"	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/network/api-gateway.png",
			label="API Gateway",
			pos="221.2,468.75",
			shape=none];
		******************************** -> "721748a1f89d40919c9e92f5b73d3323"	[dir=forward,
			fontcolor="#2D3436",
			fontname="Sans-Serif",
			fontsize=13,
			pos="e,170.93,468.75 116.66,468.75 116.66,468.75 159.41,468.75 159.41,468.75"];
	}
	subgraph "cluster_Compute Layer" {
		graph [bb="162.8,211.75,434.4,384.75",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label="Compute Layer",
			labeljust=l,
			lheight=0.18,
			lp="211.67,374.38",
			lwidth=1.14,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		"********************************"	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/compute/ec2-container-registry.png",
			label="Container Registry",
			pos="221.2,287.75",
			shape=none];
		"2421edf2b7e940e6a52c846be9919729"	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/compute/lambda.png",
			label="FastAPI Lambda",
			pos="376,287.75",
			shape=none];
		"********************************" -> "2421edf2b7e940e6a52c846be9919729"	[dir=none,
			fontcolor="#2D3436",
			fontname="Sans-Serif",
			fontsize=13,
			pos="271.46,287.75 288.85,287.75 308.34,287.75 325.73,287.75"];
	}
	subgraph "cluster_Storage & State" {
		graph [bb="478.5,392.75,757.2,565.75",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label="Storage & State",
			labeljust=l,
			lheight=0.18,
			lp="528.5,555.38",
			lwidth=1.17,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		********************************	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/storage/simple-storage-service-s3.png",
			label="Terraform State",
			pos="536.9,468.75",
			shape=none];
		"329b8ba6adcb4265b36cc6ba64d73888"	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/database/dynamodb.png",
			label="State Locks",
			pos="698.8,468.75",
			shape=none];
		******************************** -> "329b8ba6adcb4265b36cc6ba64d73888"	[dir=none,
			fontcolor="#2D3436",
			fontname="Sans-Serif",
			fontsize=13,
			pos="587.24,468.75 606.73,468.75 629,468.75 648.49,468.75"];
	}
	subgraph "cluster_Monitoring & Security" {
		graph [bb="472.4,31.75,602.4,384.75",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label="Monitoring & Security",
			labeljust=l,
			lheight=0.18,
			lp="537.4,374.38",
			lwidth=1.58,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		********************************	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/management/cloudwatch.png",
			label=CloudWatch,
			pos="536.9,107.75",
			shape=none];
		c8f960086b864bfaa2968535a26f6a17	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/security/identity-and-access-management-iam.png",
			label="IAM Roles",
			pos="536.9,287.75",
			shape=none];
	}
	subgraph "cluster_CI/CD Pipeline" {
		graph [bb="8,211.75,124.8,384.75",
			bgcolor="#E5F5FD",
			fontname="Sans-Serif",
			fontsize=12,
			label="CI/CD Pipeline",
			labeljust=l,
			lheight=0.18,
			lp="56.125,374.38",
			lwidth=1.11,
			pencolor="#AEB6BE",
			rankdir=LR,
			shape=box,
			style=rounded
		];
		"********************************"	[height=1.9,
			image="/Users/<USER>/Developer/distillery/clients/atscale/SlackGenie/.venv/lib/python3.12/site-packages/resources/aws/devtools/codebuild.png",
			label=CodeBuild,
			pos="66.4,287.75",
			shape=none];
	}
	"721748a1f89d40919c9e92f5b73d3323" -> "2421edf2b7e940e6a52c846be9919729"	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,359.2,355.9 271.41,491.55 310.87,491.55 359.2,491.55 359.2,491.55 359.2,491.55 359.2,367.41 359.2,367.41"];
	"2421edf2b7e940e6a52c846be9919729" -> ********************************	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,486.64,445.95 393.2,355.76 393.2,398.64 393.2,445.95 393.2,445.95 393.2,445.95 475.13,445.95 475.13,445.95"];
	"2421edf2b7e940e6a52c846be9919729" -> ********************************	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,486.93,107.75 376.2,219.44 376.2,168.52 376.2,107.75 376.2,107.75 376.2,107.75 475.42,107.75 475.42,107.75"];
	c8f960086b864bfaa2968535a26f6a17 -> "2421edf2b7e940e6a52c846be9919729"	[dir=none,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="486.87,287.75 467.59,287.75 445.58,287.75 426.27,287.75"];
	"********************************" -> "********************************"	[dir=forward,
		fontcolor="#2D3436",
		fontname="Sans-Serif",
		fontsize=13,
		pos="e,170.93,287.75 116.66,287.75 116.66,287.75 159.41,287.75 159.41,287.75"];
}
