from diagrams import Cluster, Diagram
from diagrams.aws.compute import Lambda
from diagrams.aws.network import APIGateway
from diagrams.aws.security import IAM, WAF
from diagrams.aws.storage import S3
from diagrams.aws.database import Dynamodb
from diagrams.aws.management import Cloudwatch
from diagrams.aws.devtools import Codebuild
from diagrams.aws.compute import ECR

with Diagram("SlackGenie AWS Architecture", filename="docgen/output/slackgenie_architecture", show=True, outformat=["jpg", "png", "dot", "svg"]):
    # API Layer
    with Cluster("API Layer"):
        waf = WAF("WAF")
        api = APIGateway("API Gateway")
        waf >> api

    # Compute Layer
    with Cluster("Compute Layer"):
        lambda_function = Lambda("FastAPI Lambda")
        ecr = ECR("Container Registry")
        ecr - lambda_function

    # Storage Layer
    with Cluster("Storage & State"):
        s3_state = S3("Terraform State")
        dynamo_locks = Dynamodb("State Locks")
        s3_state - dynamo_locks

    # Monitoring & Security
    with Cluster("Monitoring & Security"):
        cloudwatch = Cloudwatch("CloudWatch")
        iam = IAM("IAM Roles")

    # CI/CD
    with Cluster("CI/CD Pipeline"):
        codebuild = Codebuild("CodeBuild")
        codebuild >> ecr

    # Define the main flow
    api >> lambda_function
    lambda_function >> cloudwatch
    iam - lambda_function
    lambda_function >> s3_state 